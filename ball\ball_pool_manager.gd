class_name BallPoolManager
extends Node

## 弹球池管理器
##
## 负责管理弹球的创建、回收和生命周期
## 从单例模式改为普通节点模式，支持依赖注入和更好的测试

## 弹球实例池（ID -> 弹球实例）
var id_ball_pools: Dictionary[int, Node2D] = {}

## 玩家当前拥有的弹球列表
var balls: Array[Node2D] = []


func create_ball(prefab: PackedScene) -> Node:
	var node = prefab.instantiate()
	balls.append(node)
	id_ball_pools[node.get_instance_id()] = node
	return node


func get_ball(type_id: int, direction: Vector2, pos: Vector2, parent: Node = null) -> Node:
	var node = id_ball_pools.get(type_id, null)
	if node == null:
		return null
	else:
		node.global_position = pos
		if parent:
			parent.add_child(node)
		else:
			get_tree().current_scene.add_child(node)
		node.init(direction)
		node.visible = true
		return node


func recycle_ball(ball) -> void:
	if ball == null or ball.get_parent() == null:
		return
	ball.is_active = false
	ball.get_parent().remove_child(ball)
	ball.visible = false


## 获取玩家当前拥有的弹球数据
## @return: 弹球实例数组
func get_player_balls() -> Array[BallBase]:
	var player_balls: Array[BallBase] = []
	for ball in balls:
		if is_instance_valid(ball) and ball is BallBase:
			player_balls.append(ball as BallBase)
	return player_balls


## 添加新弹球到玩家的弹球列表
## @param ball_scene: 弹球场景资源
## @return: 创建的弹球实例
func add_player_ball(ball_scene: PackedScene) -> BallBase:
	var ball = create_ball(ball_scene)
	if ball is BallBase:
		print("BallPoolManager: 添加新弹球到玩家列表 - %s" % ball.name)
		return ball as BallBase
	else:
		push_error("BallPoolManager: 创建的弹球不是BallBase类型")
		return null


## 获取弹球池的统计信息
## @return: 包含统计信息的字典
func get_pool_stats() -> Dictionary:
	return {
		"total_balls": balls.size(),
		"active_balls": _count_active_balls(),
		"pool_types": id_ball_pools.size()
	}


## 计算当前活跃的弹球数量
func _count_active_balls() -> int:
	var active_count = 0
	for ball in balls:
		if is_instance_valid(ball) and ball.has_method("is_active") and ball.is_active:
			active_count += 1
	return active_count
