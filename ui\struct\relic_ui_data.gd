class_name RelicUIData
extends UIItemDataBase

## 遗物UI数据类
##
## 用于在升级面板中显示遗物信息的数据结构
## 包含遗物的名称、描述、图标、效果等UI显示所需的所有信息

## 遗物名称
var name: String = ""

## 遗物描述
var desc: String = ""
var desc_level_up: String = ""

var icon: Texture2D = null

var level: int = 1

## 遗物稀有度
var rarity_level: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON

var description: String:
	get:
		var text = "[font_size=40]%s[/font_size]\n等级 %s" % [name, level]
		if desc != "":
			text += "[font top=5]\n [/font]%s" % desc
		return text

var description_level_up: String:
	get:
		var text = "[font_size=40]%s[/font_size]\n等级 %s > %s" % [name, level - 1, level]
		if desc_level_up != "":
			text += "[font top=5]\n [/font]%s" % desc_level_up
		return text

## 构造函数
## @param p_name: 遗物名称
## @param p_description: 遗物描述
## @param p_icon: 遗物图标
## @param p_effects: 效果描述列表
## @param p_rarity: 遗物稀有度
func _init(
	p_name: String = "",
	p_desc: String = "",
	p_desc_level_up: String = "",
	p_icon = null,
	p_rarity: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON,
	p_level: int = 1
):
	name = p_name
	desc = p_desc
	desc_level_up = p_desc_level_up
	icon = p_icon
	rarity_level = p_rarity
	level = p_level


## 获取格式化的遗物信息字符串
## @return: 格式化的信息字符串
func get_formatted_info() -> String:
	var info_parts: Array[String] = []

	info_parts.append("名称: " + name)
	info_parts.append("稀有度: " + get_rarity_display_name())
	info_parts.append("描述: " + desc)

	return "\n".join(info_parts)

## 检查数据是否有效
## @return: 数据是否完整有效
func is_valid() -> bool:
	return name != "" and icon != null

## 获取稀有度的显示名称
## @return: 稀有度的中文显示名称
func get_rarity_display_name() -> String:
	match rarity_level:
		"common":
			return "普通"
		"uncommon":
			return "稀有"
		"rare":
			return "史诗"
		"epic":
			return "传说"
		"legendary":
			return "神话"
		_:
			return "未知"

## 获取稀有度对应的颜色
## @return: 稀有度对应的颜色
func get_rarity_color() -> Color:
	match rarity_level:
		"common":
			return Color.WHITE
		"uncommon":
			return Color.GREEN
		"rare":
			return Color.BLUE
		"epic":
			return Color.PURPLE
		"legendary":
			return Color.ORANGE
		_:
			return Color.GRAY


## 获取简短的显示名称（用于UI显示）
## @return: 简短的显示名称
func get_display_name() -> String:
	return name

## 创建数据的副本
## @return: 新的RelicUIData实例
func create_copy() -> RelicUIData:
	var new_data = RelicUIData.new()
	new_data.name = name
	new_data.desc = desc
	new_data.desc_level_up = desc_level_up
	new_data.icon = icon
	new_data.rarity_level = rarity_level
	new_data.level = level  # 复制等级字段
	new_data.is_upgrade_option = is_upgrade_option  # 复制基类字段
	return new_data
