extends Control

## 升级面板测试脚本
##
## 用于测试升级面板的功能，包括：
## - 显示测试数据
## - 验证交互功能
## - 检查UI布局

@onready var level_up_panel: LevelUpPanel = $LevelUpPanel

func _ready() -> void:
	# 连接面板信号
	if level_up_panel:
		level_up_panel.upgrade_selected.connect(_on_upgrade_selected)
		level_up_panel.panel_closed.connect(_on_panel_closed)

		# 延迟显示面板以确保初始化完成
		await get_tree().process_frame
		_show_test_panel()
	else:
		push_error("找不到升级面板节点")

## 显示测试面板
func _show_test_panel() -> void:
	var sample_data = _get_sample_data()
	level_up_panel.show_panel(
		sample_data.level_data,
		sample_data.balls_data,
		sample_data.relics_data,
		sample_data.options_data
	)

## 升级选择处理
func _on_upgrade_selected(upgrade_data: Dictionary) -> void:
	print("测试：升级选择完成 - ", upgrade_data.get("name", "未知选项"))

## 面板关闭处理
func _on_panel_closed() -> void:
	print("测试：升级面板已关闭")

## 获取测试数据
func _get_sample_data() -> Dictionary:
	var level_data = LevelUIData.new(5, 6, 50, 100)
	var upgrade_option_generator = UpgradeOptionGenerator.new()
	var ball_res: Array = ["res://ball/prefab/poison_ball.tscn", "res://ball/prefab/fire_ball.tscn", "res://ball/prefab/ghost_ball.tscn"]
	var balls_data: Array[BallUIData] = []
	for ball_scene_str in ball_res:
		var ball = load(ball_scene_str)
		var ball_instance = ball.instantiate()
		add_child(ball_instance)
		upgrade_option_generator.add_ball_weight_config(ball_instance, ball)
		var ui_data = UIDataConverter.ball_to_ui_data(ball_instance)
		balls_data.append(ui_data)
		ball_instance.queue_free()
	var relic_res: Array = [load("res://relic/relics/iron_armor.tres"), load("res://relic/relics/burn_oil.tres"), load("res://relic/relics/bottom_crit.tres")]
	var relics_data: Array[RelicUIData] = []
	for relic in relic_res:
		var ui_data = UIDataConverter.relic_to_ui_data(relic)
		relics_data.append(ui_data)
		upgrade_option_generator.add_relic_weight_config(relic)
	
	balls_data = balls_data.slice(0, 1)
	relics_data = relics_data.slice(0, 1)
	return {
		"level_data": level_data,
		"balls_data": balls_data,
		"relics_data": relics_data,
		"options_data": upgrade_option_generator.generate_upgrade_options(3, balls_data, relics_data)
	}


## 处理输入事件（用于测试）
func _input(event: InputEvent) -> void:
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_T:
				_show_test_panel()
			KEY_ESCAPE:
				if level_up_panel.visible:
					level_up_panel.hide_panel()
			KEY_R:
				get_tree().reload_current_scene()
