shader_type canvas_item;

/**
 * 流体轮廓Shader - 创建动态水纹效果
 * 基于Cocos Creator版本转换为Godot 4.4
 *
 * 功能：
 * - 在纹理上叠加动态水纹效果
 * - 支持自定义水纹颜色、速度、密度等参数
 * - 适用于UI元素、精灵等2D对象
 */

// ========== 可调节参数 ==========

/**
 * 时间速度控制
 * 范围：0.0 - 5.0
 * 默认：1.0
 * 说明：控制水纹动画的播放速度，数值越大动画越快
 */
uniform float time_speed : hint_range(0.0, 5.0) = 1.0;

/**
 * 水纹虚幻度/密度
 * 范围：1.0 - 50.0
 * 默认：16.1
 * 说明：控制水纹的密度和分布，数值越大水纹越多越密集
 */
uniform float ripple_density : hint_range(1.0, 50.0) = 16.1;

/**
 * 水纹颜色
 * 默认：白色 (1.0, 1.0, 1.0, 1.0)
 * 说明：设置水纹的颜色，Alpha通道控制整体透明度
 */
uniform vec4 ripple_color : source_color = vec4(1.0, 1.0, 1.0, 1.0);

/**
 * 水纹宽度/集中度
 * 范围：1 - 10
 * 默认：3
 * 说明：控制水纹的宽度，数值越大水纹越细
 */
uniform int concentration : hint_range(1, 10) = 3;

/**
 * 水纹强度
 * 范围：0.001 - 0.01
 * 默认：0.0045
 * 说明：控制水纹效果的强度，数值越大效果越明显
 */
uniform float ripple_intensity : hint_range(0.001, 0.01) = 0.0045;

/**
 * Alpha阈值
 * 范围：0.0 - 1.0
 * 默认：0.5
 * 说明：控制透明度裁剪阈值，低于此值的像素将被丢弃
 */
uniform float alpha_threshold : hint_range(0.0, 1.0) = 0.5;

/**
 * 水纹混合强度
 * 范围：0.0 - 2.0
 * 默认：1.0
 * 说明：控制水纹与原纹理的混合强度
 */
uniform float blend_strength : hint_range(0.0, 2.0) = 1.0;

// ========== Vertex Shader ==========
void vertex() {
	// 顶点着色器保持默认行为
	// UV坐标和顶点位置由Godot自动处理
}

// ========== Fragment Shader ==========
void fragment() {
	// 获取当前像素的纹理颜色
	vec4 original_color = texture(TEXTURE, UV);

	// Alpha测试 - 如果透明度低于阈值则丢弃像素
	if (original_color.a < alpha_threshold) {
		discard;
	}

	// 计算时间变量，应用速度控制
	float time = TIME * time_speed * 0.5;

	// 获取当前UV坐标
	vec2 uv = UV;

	// 计算水纹效果的基础坐标
	// 使用mod函数创建重复的水纹图案
	vec2 p = mod(uv * ripple_density, ripple_density) - 250.0;
	vec2 i = vec2(p);

	// 初始化水纹计算变量
	float c = 1.0;  // 水纹累积值

	// 生成多层水纹效果
	// 通过循环创建多个波形叠加，形成复杂的水纹图案
	for (int n = 0; n < concentration; n++) {
		// 为每一层计算不同的时间偏移
		// 这样可以创建多层波浪的叠加效果
		float t = time * (1.0 - (3.5 / float(n + 1)));

		// 计算波形坐标
		// 使用sin和cos函数创建圆形波纹
		i = p + vec2(
			cos(t - i.x) + sin(t + i.y),
			sin(t - i.y) + cos(1.5 * t + i.x)
		);

		// 累积水纹强度
		// 通过距离计算创建径向渐变效果
		c += 1.0 / length(vec2(
			p.x / (cos(i.x + t) / ripple_intensity),
			p.y / (cos(i.y + t) / ripple_intensity)
		));
	}

	// 标准化水纹强度
	c /= float(5);
	c = 1.17 - pow(c, 1.4);

	// 计算最终的水纹颜色
	// 使用pow函数增强对比度
	vec3 ripple_effect = vec3(pow(abs(c), 20.0));

	// 限制水纹效果的范围
	ripple_effect = clamp(ripple_effect + vec3(0.0, 0.0, 0.0), 0.0, original_color.a);

	// 计算水纹的Alpha混合值
	float ripple_alpha = c * original_color.a * blend_strength;

	// 将水纹效果混合到原始纹理上
	vec3 final_color = original_color.rgb + ripple_effect * ripple_alpha * ripple_color.rgb;

	// 输出最终颜色
	COLOR = vec4(final_color, original_color.a) * COLOR;
}
