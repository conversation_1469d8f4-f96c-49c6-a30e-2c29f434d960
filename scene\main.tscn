[gd_scene load_steps=28 format=3 uid="uid://dw4cvswhap11d"]

[ext_resource type="Script" uid="uid://rptd4dqfik0t" path="res://scene/main.gd" id="1_v2gmf"]
[ext_resource type="PackedScene" uid="uid://bh7jabuqr1upf" path="res://ball/prefab/blood_ball.tscn" id="2_gn2wi"]
[ext_resource type="PackedScene" uid="uid://dpxsrk2il7ehw" path="res://enemy/prefab/enemy_spawn.tscn" id="2_jcbur"]
[ext_resource type="PackedScene" uid="uid://bnjnk8a884ewg" path="res://ball/prefab/fire_ball.tscn" id="3_p8adh"]
[ext_resource type="PackedScene" uid="uid://dket7m16flssy" path="res://ball/prefab/ghost_ball.tscn" id="4_oldxj"]
[ext_resource type="PackedScene" uid="uid://bjuk4xs6y84rq" path="res://addons/damage_number_mesh/damage_manager.tscn" id="4_tv8i1"]
[ext_resource type="Shader" uid="uid://dqueevxr81bom" path="res://addons/damage_number_mesh/tonOfDamageText.gdshader" id="5_0vlqb"]
[ext_resource type="PackedScene" uid="uid://bgmy06ct41lfi" path="res://ball/prefab/poison_ball.tscn" id="5_4jm02"]
[ext_resource type="Texture2D" uid="uid://ds1ko71q8f0rr" path="res://addons/damage_number_mesh/output.png" id="6_gjnwh"]
[ext_resource type="PackedScene" uid="uid://b1gii88kkua5h" path="res://ball/prefab/shock_ball.tscn" id="6_vneqc"]
[ext_resource type="Script" uid="uid://cro1y0aapvcmp" path="res://bg/bg_manager.gd" id="7_fdqew"]
[ext_resource type="Script" uid="uid://crf0ktn1ipx6t" path="res://relic/Relic.gd" id="7_io27d"]
[ext_resource type="PackedScene" uid="uid://dvy2puti8lr3d" path="res://player/player.tscn" id="8_0d4pv"]
[ext_resource type="Resource" uid="uid://dbp2pc7pkvmck" path="res://relic/relics/iron_armor.tres" id="8_fa8t4"]
[ext_resource type="Script" uid="uid://be7w77ylk15vo" path="res://bg/env.gd" id="8_gtqqq"]
[ext_resource type="Resource" uid="uid://b4tu6qj27tbdi" path="res://bg/rock_env.tres" id="9_gn2wi"]
[ext_resource type="Resource" uid="uid://pstjyta8m8p" path="res://relic/relics/burn_oil.tres" id="9_rg6nf"]
[ext_resource type="Resource" uid="uid://dagvvt30sj82n" path="res://relic/relics/bottom_crit.tres" id="10_mjuaj"]
[ext_resource type="PackedScene" uid="uid://0kbubdtveqvo" path="res://ui/prefab/game_status_panel.tscn" id="11_k65ib"]
[ext_resource type="PackedScene" uid="uid://bm84kvlt27pga" path="res://player/dead_line.tscn" id="11_v2gmf"]
[ext_resource type="PackedScene" uid="uid://bvfj6pi6divbx" path="res://ui/prefab/level_up_panel.tscn" id="12_wscrc"]
[ext_resource type="Script" uid="uid://cw5ftpfso15bx" path="res://manager/upgrade_manager.gd" id="13_upgrade"]
[ext_resource type="Script" uid="uid://d2v51afrio7uy" path="res://ball/ball_pool_manager.gd" id="14_ball_pool"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_pm3ni"]
shader = ExtResource("5_0vlqb")
shader_parameter/numbersTex = ExtResource("6_gjnwh")
shader_parameter/digit_spacing = 0.0
shader_parameter/uv_inset = Vector2(0.21, 0)
shader_parameter/stroke_thickness = 3.0
shader_parameter/stroke_color = Color(0, 0, 0, 1)
shader_parameter/pattern = 0
shader_parameter/inside = false

[sub_resource type="MultiMesh" id="MultiMesh_og1vs"]
use_colors = true
use_custom_data = true
visible_instance_count = 0

[sub_resource type="Environment" id="Environment_sugp2"]
background_mode = 3
glow_enabled = true

[sub_resource type="PhysicsMaterial" id="PhysicsMaterial_k65ib"]
friction = 0.0
bounce = 1.0

[node name="Main" type="Node2D"]
script = ExtResource("1_v2gmf")
ball_scenes = Array[PackedScene]([ExtResource("2_gn2wi"), ExtResource("3_p8adh"), ExtResource("4_oldxj"), ExtResource("5_4jm02"), ExtResource("6_vneqc")])
relic_resources = Array[ExtResource("7_io27d")]([ExtResource("8_fa8t4"), ExtResource("9_rg6nf"), ExtResource("10_mjuaj")])

[node name="Managers" type="Node" parent="."]

[node name="DamageMeshManager" parent="Managers" instance=ExtResource("4_tv8i1")]
material = SubResource("ShaderMaterial_pm3ni")
multimesh = SubResource("MultiMesh_og1vs")
base_scale = Vector2(0.4, 2.3)
global_acceleration = Vector2(0, 0)
initial_velocity_min = Vector2(0, -80)
initial_velocity_max = Vector2(0, -80)
lifetime = 0.6

[node name="BGManager" type="Node" parent="Managers"]
script = ExtResource("7_fdqew")
environment_themes = Array[ExtResource("8_gtqqq")]([ExtResource("9_gn2wi")])
metadata/_custom_type_script = "uid://cro1y0aapvcmp"

[node name="UpgradeManager" type="Node" parent="Managers"]
script = ExtResource("13_upgrade")

[node name="BallPoolManager" type="Node" parent="Managers" groups=["ball_pool_manager"]]
script = ExtResource("14_ball_pool")

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(360, 640)

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_sugp2")

[node name="EnemySpawn" parent="." instance=ExtResource("2_jcbur")]
y_sort_enabled = true
grid_rows = 2
grid_cell_height = 90

[node name="Player" parent="." instance=ExtResource("8_0d4pv")]
position = Vector2(361, 864)
move_boundary = Rect2(20, 40, 680, 1000)

[node name="DeadLine" parent="." instance=ExtResource("11_v2gmf")]

[node name="Border" type="StaticBody2D" parent="."]
collision_mask = 18
physics_material_override = SubResource("PhysicsMaterial_k65ib")

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="Border"]
polygon = PackedVector2Array(0, 0, 0, 1280, -20, 1280, -20, -20, 740, -20, 740, 1280, 720, 1280, 720, 0)

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="GameStatusPanel" parent="CanvasLayer" instance=ExtResource("11_k65ib")]

[node name="LevelUpPanel" parent="CanvasLayer" instance=ExtResource("12_wscrc")]
visible = false

[node name="Setting" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0
