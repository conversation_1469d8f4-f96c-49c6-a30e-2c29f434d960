[gd_resource type="Theme" load_steps=48 format=3 uid="uid://bmf31mho1mjjt"]

[ext_resource type="Texture2D" uid="uid://bef816edvcpop" path="res://ui/sprout_lands_ui/assets/Sprite sheet for Basic Pack.png" id="1_0l15s"]
[ext_resource type="Texture2D" uid="uid://c40is5g24qrm8" path="res://ui/img/Pxiel Art UI borders.png" id="2_85anh"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_w4jy1"]
content_margin_left = 8.0
content_margin_top = 8.0
content_margin_right = 8.0
content_margin_bottom = 8.0
texture = ExtResource("1_0l15s")
texture_margin_left = 4.0
texture_margin_top = 4.0
texture_margin_right = 4.0
texture_margin_bottom = 4.0
region_rect = Rect2(63, 111, 18, 18)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_ytkor"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 9.0
texture_margin_right = 6.0
texture_margin_bottom = 6.0
region_rect = Rect2(375, 5, 18, 20)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_7ctvm"]
texture = ExtResource("1_0l15s")
texture_margin_left = 8.0
texture_margin_top = 9.0
texture_margin_right = 8.0
texture_margin_bottom = 9.0
expand_margin_left = 4.0
expand_margin_top = 4.0
expand_margin_right = 4.0
expand_margin_bottom = 4.0
region_rect = Rect2(180, 148, 24, 25)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_krtg3"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 8.0
texture_margin_right = 6.0
texture_margin_bottom = 8.0
region_rect = Rect2(343, 100, 18, 21)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_hfb1j"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 6.0
texture_margin_right = 6.0
texture_margin_bottom = 9.0
region_rect = Rect2(311, 101, 18, 20)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_l3rb3"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 6.0
texture_margin_right = 6.0
texture_margin_bottom = 9.0
region_rect = Rect2(311, 101, 18, 20)

[sub_resource type="AtlasTexture" id="AtlasTexture_43wcw"]
atlas = ExtResource("1_0l15s")
region = Rect2(242, 67, 12, 12)
margin = Rect2(4, 4, 6, 0)

[sub_resource type="AtlasTexture" id="AtlasTexture_l7reh"]
atlas = ExtResource("1_0l15s")
region = Rect2(242, 82, 12, 12)
margin = Rect2(4, 0, 8, 2)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_effect_desc"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 6.0
texture_margin_right = 6.0
texture_margin_bottom = 6.0
region_rect = Rect2(63, 111, 18, 18)
modulate_color = Color(0, 0, 0, 0.494118)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_6gpgw"]
texture = ExtResource("1_0l15s")
texture_margin_left = 4.0
texture_margin_top = 3.0
texture_margin_right = 4.0
texture_margin_bottom = 4.0
expand_margin_top = 3.0
expand_margin_bottom = 2.0
region_rect = Rect2(247, 164, 18, 8)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_22jjy"]
texture = ExtResource("1_0l15s")
texture_margin_left = 4.0
texture_margin_top = 3.0
texture_margin_right = 4.0
texture_margin_bottom = 4.0
expand_margin_top = 3.0
expand_margin_bottom = 2.0
region_rect = Rect2(247, 148, 18, 8)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_m2s22"]
texture = ExtResource("1_0l15s")
texture_margin_left = 4.0
texture_margin_top = 3.0
texture_margin_right = 4.0
texture_margin_bottom = 4.0
expand_margin_top = 3.0
expand_margin_bottom = 2.0
region_rect = Rect2(247, 132, 18, 8)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_ia7ny"]
texture = ExtResource("1_0l15s")
texture_margin_left = 2.0
texture_margin_top = 2.0
texture_margin_right = 2.0
texture_margin_bottom = 1.0
expand_margin_top = 3.0
expand_margin_bottom = 2.0
region_rect = Rect2(277, 166, 38, 4)

[sub_resource type="AtlasTexture" id="AtlasTexture_1pgc0"]
atlas = ExtResource("1_0l15s")
region = Rect2(375, 71, 18, 20)

[sub_resource type="AtlasTexture" id="AtlasTexture_w81rj"]
atlas = ExtResource("1_0l15s")
region = Rect2(375, 7, 18, 20)

[sub_resource type="AtlasTexture" id="AtlasTexture_pu3vw"]
atlas = ExtResource("1_0l15s")
region = Rect2(343, 38, 18, 20)

[sub_resource type="AtlasTexture" id="AtlasTexture_i5hmu"]
atlas = ExtResource("1_0l15s")
region = Rect2(292, 35, 8, 5)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_y4yfd"]
texture = ExtResource("1_0l15s")
texture_margin_left = 2.0
texture_margin_top = 3.0
texture_margin_right = 2.0
texture_margin_bottom = 2.0
expand_margin_top = 3.0
expand_margin_bottom = 3.0
region_rect = Rect2(466, 73, 28, 14)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_4r8ma"]
texture = ExtResource("1_0l15s")
texture_margin_left = 2.0
texture_margin_top = 3.0
texture_margin_right = 2.0
texture_margin_bottom = 2.0
expand_margin_top = 3.0
expand_margin_bottom = 3.0
region_rect = Rect2(402, 73, 28, 14)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_w6s65"]
texture = ExtResource("1_0l15s")
texture_margin_left = 1.0
texture_margin_top = 1.0
texture_margin_right = 1.0
texture_margin_bottom = 1.0
expand_margin_top = 3.0
expand_margin_bottom = 3.0
region_rect = Rect2(277, 166, 38, 4)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_f2x38"]
texture = ExtResource("2_85anh")
texture_margin_left = 15.0
texture_margin_top = 15.0
texture_margin_right = 15.0
texture_margin_bottom = 15.0
region_rect = Rect2(19, 275, 90, 90)
modulate_color = Color(0.667237, 0.554871, 0.609879, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_85anh"]
texture = ExtResource("2_85anh")
texture_margin_left = 15.0
texture_margin_top = 15.0
texture_margin_right = 15.0
texture_margin_bottom = 15.0
region_rect = Rect2(19, 275, 90, 90)
modulate_color = Color(0.667237, 0.554871, 0.609879, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_0l15s"]
texture = ExtResource("2_85anh")
texture_margin_left = 15.0
texture_margin_top = 15.0
texture_margin_right = 15.0
texture_margin_bottom = 15.0
region_rect = Rect2(19, 275, 90, 90)
modulate_color = Color(0.667237, 0.554871, 0.609879, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_oecrf"]
texture = ExtResource("2_85anh")
texture_margin_left = 15.0
texture_margin_top = 15.0
texture_margin_right = 15.0
texture_margin_bottom = 15.0
region_rect = Rect2(19, 275, 90, 90)
modulate_color = Color(0.667237, 0.554871, 0.609879, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_5h33j"]
texture = ExtResource("2_85anh")
texture_margin_left = 15.0
texture_margin_top = 15.0
texture_margin_right = 15.0
texture_margin_bottom = 15.0
region_rect = Rect2(18, 274, 92, 92)
modulate_color = Color(0.520753, 0.413522, 0.466662, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_10p3a"]
texture = ExtResource("2_85anh")
texture_margin_left = 10.0
texture_margin_top = 10.0
texture_margin_right = 10.0
texture_margin_bottom = 10.0
region_rect = Rect2(306, 146, 92, 92)
modulate_color = Color(0.44084, 0.34413, 0.392227, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_hdkho"]
texture = ExtResource("2_85anh")
texture_margin_left = 20.0
texture_margin_top = 20.0
texture_margin_right = 20.0
texture_margin_bottom = 20.0
region_rect = Rect2(17, 17, 94, 94)
modulate_color = Color(0.77333, 0.519639, 0.123035, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_xbpyb"]
texture = ExtResource("1_0l15s")
texture_margin_left = 3.0
texture_margin_top = 4.0
texture_margin_right = 3.0
texture_margin_bottom = 2.0
axis_stretch_horizontal = 1
region_rect = Rect2(402, 105, 28, 14)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_avs0p"]
texture = ExtResource("1_0l15s")
texture_margin_left = 2.0
texture_margin_top = 4.0
texture_margin_right = 2.0
texture_margin_bottom = 2.0
region_rect = Rect2(402, 73, 28, 14)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_b5k0e"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 8.0
texture_margin_right = 6.0
texture_margin_bottom = 8.0
region_rect = Rect2(343, 68, 18, 21)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_0dk3e"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 6.0
texture_margin_right = 6.0
texture_margin_bottom = 9.0
region_rect = Rect2(311, 69, 18, 20)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_f8y6s"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 10.0
texture_margin_right = 6.0
texture_margin_bottom = 6.0
region_rect = Rect2(375, 68, 18, 21)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_a7ncx"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 9.0
texture_margin_right = 6.0
region_rect = Rect2(375, 101, 18, 14)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_pkuo0"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 6.0
texture_margin_right = 6.0
region_rect = Rect2(375, 72, 18, 11)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_vgud8"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 6.0
texture_margin_right = 6.0
region_rect = Rect2(311, 69, 18, 11)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_qdddf"]
texture = ExtResource("1_0l15s")
texture_margin_left = 6.0
texture_margin_top = 9.0
texture_margin_right = 6.0
region_rect = Rect2(375, 69, 18, 14)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_02d5e"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_m0caa"]
texture = ExtResource("1_0l15s")
texture_margin_left = 3.0
texture_margin_top = 4.0
texture_margin_right = 3.0
texture_margin_bottom = 5.0
expand_margin_left = 1.0
region_rect = Rect2(277, 133, 7, 18)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_241c5"]
texture = ExtResource("1_0l15s")
texture_margin_left = 3.0
texture_margin_top = 4.0
texture_margin_right = 3.0
texture_margin_bottom = 5.0
expand_margin_left = 1.0
region_rect = Rect2(293, 133, 7, 18)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_ta4qm"]
texture = ExtResource("1_0l15s")
texture_margin_left = 3.0
texture_margin_top = 4.0
texture_margin_right = 3.0
texture_margin_bottom = 5.0
expand_margin_left = 1.0
region_rect = Rect2(309, 133, 7, 18)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_si1xo"]
texture = ExtResource("1_0l15s")
texture_margin_left = 3.0
texture_margin_top = 3.0
texture_margin_right = 3.0
texture_margin_bottom = 4.0
expand_margin_left = 1.0
region_rect = Rect2(326, 133, 4, 38)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iefc1"]

[sub_resource type="AtlasTexture" id="AtlasTexture_kk8tp"]
atlas = ExtResource("1_0l15s")
region = Rect2(818, 226, 12, 12)

[sub_resource type="AtlasTexture" id="AtlasTexture_kydnu"]
atlas = ExtResource("1_0l15s")
region = Rect2(674, 226, 12, 12)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_2bxh0"]
texture = ExtResource("1_0l15s")
texture_margin_left = 4.0
texture_margin_top = 4.0
texture_margin_right = 4.0
texture_margin_bottom = 5.0
expand_margin_left = 3.0
expand_margin_top = 32.0
expand_margin_right = 3.0
expand_margin_bottom = 4.0
region_rect = Rect2(107, 155, 26, 26)

[resource]
AcceptDialog/styles/panel = SubResource("StyleBoxTexture_w4jy1")
Button/styles/disabled = SubResource("StyleBoxTexture_ytkor")
Button/styles/focus = SubResource("StyleBoxTexture_7ctvm")
Button/styles/hover = SubResource("StyleBoxTexture_krtg3")
Button/styles/normal = SubResource("StyleBoxTexture_hfb1j")
Button/styles/pressed = SubResource("StyleBoxTexture_l3rb3")
ButtonDeep/base_type = &"Button"
ButtonDeep/colors/font_color = Color(0.101961, 0.0705882, 0.0156863, 1)
CheckButton/icons/checked = SubResource("AtlasTexture_43wcw")
CheckButton/icons/unchecked = SubResource("AtlasTexture_l7reh")
CheckButton/styles/hover_pressed = SubResource("StyleBoxTexture_krtg3")
EffectDescription/base_type = &"PanelContainer"
EffectDescription/styles/panel = SubResource("StyleBoxTexture_effect_desc")
HScrollBar/styles/grabber = SubResource("StyleBoxTexture_6gpgw")
HScrollBar/styles/grabber_highlight = SubResource("StyleBoxTexture_22jjy")
HScrollBar/styles/grabber_pressed = SubResource("StyleBoxTexture_m2s22")
HScrollBar/styles/scroll = SubResource("StyleBoxTexture_ia7ny")
HSlider/icons/grabber = SubResource("AtlasTexture_1pgc0")
HSlider/icons/grabber_disabled = SubResource("AtlasTexture_w81rj")
HSlider/icons/grabber_highlight = SubResource("AtlasTexture_pu3vw")
HSlider/icons/tick = SubResource("AtlasTexture_i5hmu")
HSlider/styles/grabber_area = SubResource("StyleBoxTexture_y4yfd")
HSlider/styles/grabber_area_highlight = SubResource("StyleBoxTexture_4r8ma")
HSlider/styles/slider = SubResource("StyleBoxTexture_w6s65")
IconSlot/base_type = &"Button"
IconSlot/styles/focus = SubResource("StyleBoxTexture_f2x38")
IconSlot/styles/hover = SubResource("StyleBoxTexture_85anh")
IconSlot/styles/normal = SubResource("StyleBoxTexture_0l15s")
IconSlot/styles/pressed = SubResource("StyleBoxTexture_oecrf")
ItemSlotPanel/base_type = &"Panel"
ItemSlotPanel/styles/panel = SubResource("StyleBoxTexture_5h33j")
ItemSlotPanelSub/base_type = &"Panel"
ItemSlotPanelSub/styles/panel = SubResource("StyleBoxTexture_10p3a")
Label/colors/font_color = Color(0.933333, 0.870588, 0.909804, 1)
Panel/styles/panel = SubResource("StyleBoxTexture_hdkho")
PopupDialog/styles/panel = SubResource("StyleBoxTexture_w4jy1")
PopupPanel/styles/panel = SubResource("StyleBoxTexture_w4jy1")
ProgressBar/styles/background = SubResource("StyleBoxTexture_xbpyb")
ProgressBar/styles/fill = SubResource("StyleBoxTexture_avs0p")
SkipButton/base_type = &"Button"
SkipButton/styles/focus = SubResource("StyleBoxTexture_7ctvm")
SkipButton/styles/hover = SubResource("StyleBoxTexture_b5k0e")
SkipButton/styles/normal = SubResource("StyleBoxTexture_0dk3e")
SkipButton/styles/pressed = SubResource("StyleBoxTexture_f8y6s")
TabContainer/styles/panel = SubResource("StyleBoxTexture_hdkho")
TabContainer/styles/tab_disabled = SubResource("StyleBoxTexture_a7ncx")
TabContainer/styles/tab_hovered = SubResource("StyleBoxTexture_pkuo0")
TabContainer/styles/tab_selected = SubResource("StyleBoxTexture_vgud8")
TabContainer/styles/tab_unselected = SubResource("StyleBoxTexture_qdddf")
Tree/styles/panel = SubResource("StyleBoxEmpty_02d5e")
VScrollBar/styles/grabber = SubResource("StyleBoxTexture_m0caa")
VScrollBar/styles/grabber_highlight = SubResource("StyleBoxTexture_241c5")
VScrollBar/styles/grabber_pressed = SubResource("StyleBoxTexture_ta4qm")
VScrollBar/styles/scroll = SubResource("StyleBoxTexture_si1xo")
VScrollBar/styles/scroll_focus = SubResource("StyleBoxEmpty_iefc1")
Window/colors/title_color = Color(1, 1, 1, 1)
Window/colors/title_outline_modulate = Color(1, 1, 1, 1)
Window/icons/close = SubResource("AtlasTexture_kk8tp")
Window/icons/close_pressed = SubResource("AtlasTexture_kydnu")
Window/styles/embedded_border = SubResource("StyleBoxTexture_2bxh0")
