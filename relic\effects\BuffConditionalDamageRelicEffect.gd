class_name ConditionalDamageRelicEffect extends RelicEffect

## 当击中带有着火的敌人时，要施加给弹球的Buff。
## 注意：这个Buff资源本身应该被配置为【有时效的】，例如设置一个非常短的持续时间。
@export var buff_to_apply: AttributeBuff

## 敌人需要拥有的Buff名称，例如 "burn"
@export var required_enemy_buff_name: StringName = "burn"

## 弹球身上将要接收这个临时伤害Buff的属性名称。
@export var ball_target_attribute: StringName = "extra_damage"


func on_acquired(_target: Node):
	# 订阅“球击中敌人”事件
	GameEvents.ball_hit_enemy.connect(_on_ball_hit_enemy)

func on_lost(_target: Node):
	# 确保在失去遗物时断开连接，防止错误
	if GameEvents.is_connected("ball_hit_enemy", Callable(self, "_on_ball_hit_enemy")):
		GameEvents.ball_hit_enemy.disconnect(_on_ball_hit_enemy)

func _on_ball_hit_enemy(ball: Node, enemy: Node, _collision_info: Dictionary):
	# 1. 检查敌人是否拥有我们需要的Buff（例如“灼烧”）
	var enemy_attr_comp = enemy.get_node_or_null("AttributeComponent") as AttributeComponent
	if not is_instance_valid(enemy_attr_comp):
		return

	# 为简单起见，我们假设状态类Buff（如灼烧、中毒）都附加在“hp”属性上
	var enemy_hp_attr = enemy_attr_comp.find_attribute("hp")
	if not is_instance_valid(enemy_hp_attr) or not is_instance_valid(enemy_hp_attr.find_buff(required_enemy_buff_name)):
		# 敌人没有灼烧状态，直接返回
		return
	
	# 2. 敌人正在灼烧，给弹球施加增伤Buff
	var ball_attr_comp = ball.get_node_or_null("AttributeComponent") as AttributeComponent
	if not is_instance_valid(ball_attr_comp):
		push_warning("遗物效果失败：弹球 '%s' 缺少 AttributeComponent." % ball.name)
		return
		
	var ball_damage_attr = ball_attr_comp.find_attribute(ball_target_attribute)
	if is_instance_valid(ball_damage_attr):
		ball_damage_attr.add_buff(buff_to_apply)
	else:
		push_warning("遗物效果失败：弹球 '%s' 缺少属性 '%s'." % [ball.name, ball_target_attribute]) 

func get_attribute_set() -> AttributeSet:
	if is_instance_valid(buff_to_apply):
		return buff_to_apply.attribute_set
	return null