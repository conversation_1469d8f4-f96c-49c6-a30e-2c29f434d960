[gd_resource type="Resource" script_class="Relic" load_steps=16 format=3 uid="uid://dagvvt30sj82n"]

[ext_resource type="Script" uid="uid://crf0ktn1ipx6t" path="res://relic/Relic.gd" id="1_580oa"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="1_kihru"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="2_00hd1"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="3_ebs3j"]
[ext_resource type="Script" uid="uid://h4qchw2pf3g7" path="res://addons/attribute_manager/AttributeBuffCount.gd" id="4_kihru"]
[ext_resource type="Script" uid="uid://dapvw2kkpib0i" path="res://relic/effects/PosConditionRelicEffect.gd" id="5_7sho6"]
[ext_resource type="Texture2D" uid="uid://c46f658slia78" path="res://assets/imgs/effects/34.png" id="6_q3elq"]

[sub_resource type="Resource" id="Resource_00hd1"]
script = ExtResource("1_kihru")
base_value = 0.0
can_cache = true

[sub_resource type="Resource" id="Resource_7sho6"]
script = ExtResource("1_kihru")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_cp8xw"]
script = ExtResource("1_kihru")
base_value = 1.0
can_cache = true

[sub_resource type="Resource" id="Resource_ebs3j"]
script = ExtResource("1_kihru")
base_value = 1.0
can_cache = true

[sub_resource type="Resource" id="Resource_kihru"]
script = ExtResource("2_00hd1")
growth_per_level = 10.0
base_value = 20.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_55mqa"]
script = ExtResource("3_ebs3j")
attributes = Dictionary[StringName, ExtResource("1_kihru")]({
&"duration": SubResource("Resource_00hd1"),
&"level": SubResource("Resource_7sho6"),
&"max_counts": SubResource("Resource_cp8xw"),
&"max_stacks": SubResource("Resource_ebs3j"),
&"value": SubResource("Resource_kihru")
})

[sub_resource type="Resource" id="Resource_b0yov"]
script = ExtResource("4_kihru")
buff_name = "add_bottom_crit"
operation = 0
policy = 0
merging = 2
attribute_set = SubResource("Resource_55mqa")
metadata/_custom_type_script = "uid://h4qchw2pf3g7"

[sub_resource type="Resource" id="Resource_ruoqw"]
script = ExtResource("5_7sho6")
buff_to_apply = SubResource("Resource_b0yov")
apply_attribute = "crit"
distance_threshold = 40.0
pos_condition = 0
metadata/_custom_type_script = "uid://dapvw2kkpib0i"

[resource]
script = ExtResource("1_580oa")
name = "金色匕首"
description = "从下方攻击时暴击率提高"
description_level_up = ""
icon = ExtResource("6_q3elq")
effect = SubResource("Resource_ruoqw")
rarity_level = 0
rarity_weight = 8.0
metadata/_custom_type_script = "uid://crf0ktn1ipx6t"
