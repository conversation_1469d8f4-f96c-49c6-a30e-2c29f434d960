[gd_scene load_steps=36 format=3 uid="uid://dket7m16flssy"]

[ext_resource type="PhysicsMaterial" uid="uid://d0d4n7iiiu1o8" path="res://assets/materials/physics/ball.tres" id="1_qk82s"]
[ext_resource type="Script" uid="uid://drjqkf3mt3gs1" path="res://ball/ball_base.gd" id="2_f5o85"]
[ext_resource type="Script" uid="uid://boyhjsm72rmvx" path="res://addons/shader_sprite/shaders_sprite_2d.gd" id="3_6hn6j"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="3_aues4"]
[ext_resource type="Material" uid="uid://u3rtsamehui" path="res://ball/effect/ball_sprite_material/ghost_material.tres" id="3_f5o85"]
[ext_resource type="Script" uid="uid://boltc4gkngyox" path="res://ui/res/ball_ui_resource.gd" id="3_trs1y"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="3_vsofp"]
[ext_resource type="Script" uid="uid://c4hm0ggsbq3bl" path="res://addons/trail/trail.gd" id="4_6hn6j"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="4_b74hx"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="5_k30cu"]
[ext_resource type="Script" uid="uid://cb0b7m4niewhd" path="res://ball/ability/ghost_ability.gd" id="5_x20ks"]
[ext_resource type="Material" uid="uid://bsy5vd8ya1x8u" path="res://ball/effect/ball_sprite_material/ghost_ring_material.tres" id="6_6hn6j"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="6_ch4bg"]
[ext_resource type="Shader" uid="uid://b7lnuflpbunq7" path="res://assets/materials/shader/fluid.gdshader" id="7_x20ks"]
[ext_resource type="Script" uid="uid://qu5oshh0ccl5" path="res://attribute/ball_damage_attribute.gd" id="8_nq8r7"]
[ext_resource type="Script" uid="uid://b14ywpmhnqjgo" path="res://ui/res/ability_ui_resource.gd" id="15_rd6lk"]

[sub_resource type="Resource" id="Resource_rd6lk"]
script = ExtResource("3_trs1y")
ball_name = "幽灵"
description = "命中伤害 [color=white][min_damage]-[max_damage][/color]"
description_level_up = "命中伤害 [color=white][min_damage]-[max_damage][/color] > [color=white][min_damage+1]-[max_damage+1][/color]"
icon_type = 2
icon_text = ""
icon_shader_material = ExtResource("6_6hn6j")
rarity_level = 2
rarity_weight = 3.0
metadata/_custom_type_script = "uid://boltc4gkngyox"

[sub_resource type="Resource" id="Resource_jqmq8"]
script = ExtResource("4_b74hx")
base_value = 5.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_boo4p"]
script = ExtResource("8_nq8r7")
min_value_source = "min_damage"
max_value_source = "max_damage"
crit_rate_source = "crit"
base_value = 0.0
can_cache = true
metadata/_custom_type_script = "uid://qu5oshh0ccl5"

[sub_resource type="Resource" id="Resource_6rv2f"]
script = ExtResource("4_b74hx")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_2p4io"]
script = ExtResource("5_k30cu")
growth_per_level = 15.0
base_value = 25.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_16514"]
script = ExtResource("5_k30cu")
growth_per_level = 10.0
base_value = 15.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_aues4"]
script = ExtResource("4_b74hx")
base_value = 500.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_n6p6h"]
resource_local_to_scene = true
script = ExtResource("6_ch4bg")
attributes = Dictionary[StringName, ExtResource("4_b74hx")]({
&"crit": SubResource("Resource_jqmq8"),
&"damage": SubResource("Resource_boo4p"),
&"level": SubResource("Resource_6rv2f"),
&"max_damage": SubResource("Resource_2p4io"),
&"min_damage": SubResource("Resource_16514"),
&"speed": SubResource("Resource_aues4")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_qc54y"]
noise_type = 2
frequency = 0.013
fractal_type = 2
fractal_octaves = 6
fractal_lacunarity = 1.18
fractal_gain = 1.885

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_oiqqk"]
width = 557
height = 227
seamless = true
noise = SubResource("FastNoiseLite_qc54y")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_dl56t"]
frequency = 0.0016
fractal_type = 2

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_a0w6y"]
seamless = true
noise = SubResource("FastNoiseLite_dl56t")

[sub_resource type="Gradient" id="Gradient_aues4"]
offsets = PackedFloat32Array(0, 0.677007, 1)
colors = PackedColorArray(1, 1, 1, 0, 0, 0.605667, 0.79, 1, 0.4661, 0.714423, 0.79, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_b74hx"]
gradient = SubResource("Gradient_aues4")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_k30cu"]
shader = ExtResource("7_x20ks")
shader_parameter/noise1 = SubResource("NoiseTexture2D_oiqqk")
shader_parameter/noise2 = SubResource("NoiseTexture2D_a0w6y")
shader_parameter/scroll1 = Vector2(0.15, 0.25)
shader_parameter/scroll2 = Vector2(-0.15, -0.25)
shader_parameter/tex2_scale = 1.0
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_b74hx")
shader_parameter/overlap_factor = 1.3
shader_parameter/color_factor = 10.0
shader_parameter/blur = 1.0
shader_parameter/delay_v = 7.3
shader_parameter/delay_type = 0
shader_parameter/embed = true
shader_parameter/edge_threshold = 0.45
shader_parameter/edge_softness = 0.31
shader_parameter/edge_noise_scale = 0.7
shader_parameter/edge_noise_influence = 1.0
shader_parameter/edge_noise_scroll = Vector2(0.05, 0.03)
shader_parameter/edge_direction_mode = 0
shader_parameter/use_multiple_edges = true
shader_parameter/edge_left = true
shader_parameter/edge_right = false
shader_parameter/edge_top = true
shader_parameter/edge_bottom = true
shader_parameter/edge_radial = false
shader_parameter/edge_animation_speed = 5.0

[sub_resource type="Curve" id="Curve_yi17e"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Gradient" id="Gradient_yi17e"]
colors = PackedColorArray(1, 1, 1, 0, 1, 1, 1, 0)

[sub_resource type="CircleShape2D" id="CircleShape2D_yi17e"]
radius = 21.0238

[sub_resource type="Resource" id="Resource_nq8r7"]
script = ExtResource("15_rd6lk")
description = "可以穿透敌人"
description_level_up = "可以穿透敌人"
metadata/_custom_type_script = "uid://b14ywpmhnqjgo"

[node name="GhostBall" type="RigidBody2D"]
collision_layer = 16
physics_material_override = ExtResource("1_qk82s")
gravity_scale = 0.0
can_sleep = false
lock_rotation = true
linear_damp_mode = 1
angular_damp_mode = 1
script = ExtResource("2_f5o85")
ui_resource = SubResource("Resource_rd6lk")
ball_type = 1
cooldown = 30.0
acceleration_multiplier = 0.5

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("3_aues4")
attribute_set = SubResource("Resource_n6p6h")
metadata/_custom_type_script = "uid://3lqxnj5nr7f1"

[node name="Sprite2D" type="Sprite2D" parent="."]
material = ExtResource("3_f5o85")
scale = Vector2(0.4, 0.4)
script = ExtResource("3_6hn6j")
shaders_texture = ExtResource("3_vsofp")
shaders_dic = {
&"fruid": ExtResource("3_f5o85"),
&"ring": ExtResource("6_6hn6j")
}
metadata/_custom_type_script = "uid://boyhjsm72rmvx"

[node name="Trail" type="Line2D" parent="Sprite2D"]
top_level = true
z_index = -1
material = SubResource("ShaderMaterial_k30cu")
width = 35.0
width_curve = SubResource("Curve_yi17e")
gradient = SubResource("Gradient_yi17e")
texture_mode = 2
joint_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("4_6hn6j")
max_points = 40

[node name="Vis" type="Sprite2D" parent="."]
visible = false
material = ExtResource("6_6hn6j")
texture = ExtResource("3_vsofp")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource("CircleShape2D_yi17e")

[node name="Abilities" type="Node" parent="."]

[node name="GhostAbility" type="Node" parent="Abilities"]
script = ExtResource("5_x20ks")
ui_resource = SubResource("Resource_nq8r7")
metadata/_custom_type_script = "uid://cb0b7m4niewhd"
