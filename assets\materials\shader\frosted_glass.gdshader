/*
    毛玻璃效果 Shader（扭曲效果）
    基于 Shadertoy 代码转换为 Godot 4.4 兼容格式

    功能：
    - 静态毛玻璃扭曲效果
    - 支持透明度调节
    - 支持效果强度控制
    - 支持颜色叠加
*/

shader_type canvas_item;

// ========== 基础参数 ==========

/**
 * 毛玻璃颜色
 * 叠加在扭曲效果上的颜色
 */
uniform vec4 glass_color : source_color = vec4(1.0, 1.0, 1.0, 1.0);

/**
 * 扭曲强度
 * 控制毛玻璃扭曲效果的强度
 */
uniform float distortion_strength : hint_range(0.0, 2.0) = 1.0;

/**
 * 噪声扭曲强度
 * 控制噪声纹理对扭曲的影响程度
 */
uniform float noise_distortion : hint_range(0.0, 0.2) = 0.05;

/**
 * 噪声缩放
 * 控制噪声纹理的缩放程度，影响扭曲的细节密度
 */
uniform float noise_scale : hint_range(0.1, 10.0) = 1.0;

// ========== 纹理输入 ==========
/**
 * 屏幕纹理
 * 用于获取背景内容实现毛玻璃效果
 */
uniform sampler2D screen_texture : hint_screen_texture, repeat_disable, filter_linear;

/**
 * 噪声纹理
 * 用于产生扭曲效果的噪声纹理
 * 如果不提供，将使用程序化噪声
 */
uniform sampler2D noise_texture : repeat_enable, filter_linear;

/**
 * 是否使用噪声纹理
 * 如果为 false，将使用程序化噪声
 */
uniform bool use_noise_texture = false;

// ========== 辅助函数 ==========
/**
 * 简单程序化噪声函数
 * 当没有提供噪声纹理时使用
 * 生成基于位置的静态噪声
 */
vec2 simple_noise(vec2 pos) {
    return fract(vec2(
        sin(dot(pos, vec2(12.9898, 78.233))) * 43758.5453,
        sin(dot(pos, vec2(93.9898, 67.345))) * 28653.2341
    ));
}

/**
 * 静态扭曲计算函数
 * 计算基于噪声的静态UV扭曲，覆盖整个区域
 */
vec2 calculate_distortion(vec2 uv) {
    // 使用缩放后的UV坐标采样噪声
    vec2 noise_uv = uv * noise_scale;

    // 获取噪声值
    vec2 noise_value;
    if (use_noise_texture) {
        noise_value = texture(noise_texture, noise_uv).xy;
        // 将噪声值从 [0,1] 范围转换到 [-1,1] 范围
        noise_value = (noise_value - 0.5) * 2.0;
    } else {
        noise_value = simple_noise(noise_uv * 10.0);
        // 将噪声值从 [0,1] 范围转换到 [-1,1] 范围
        noise_value = (noise_value - 0.5) * 2.0;
    }

    // 应用扭曲，整个区域都有扭曲效果
    vec2 distortion = noise_value * noise_distortion * distortion_strength;

    return uv + distortion;
}

void fragment() {
    // 毛玻璃扭曲效果
    vec2 distorted_uv = calculate_distortion(UV);

    // 将UV坐标转换为屏幕坐标进行采样
    vec2 screen_uv = SCREEN_UV + (distorted_uv - UV);

    // 确保屏幕坐标在有效范围内
    screen_uv = clamp(screen_uv, 0.0, 1.0);

    // 采样背景纹理
    vec4 background_color = texture(screen_texture, screen_uv);
    
    float alpha = glass_color.a;
    // 混合颜色：当alpha接近0时，主要显示背景；当alpha接近1时，显示更多玻璃颜色
    vec4 final_color = mix(background_color, background_color * glass_color, alpha);

    // 应用透明度
    COLOR = vec4(final_color.rgb, alpha);
}
