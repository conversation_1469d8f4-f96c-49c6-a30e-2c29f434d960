class_name EnemyBullet
extends Area2D

@onready var attribute_component: AttributeComponent = $AttributeComponent
@onready var sprite: Sprite2D = $Sprite2D

## 子弹移动方向
var direction: Vector2 = Vector2.DOWN
## 子弹是否激活
var is_active: bool = false
## 屏幕边界（用于销毁检测）
var screen_bounds: Rect2

# 属性访问器
var damage: float:
	get:
		return attribute_component.get_attribute_value("damage") if attribute_component else 0.0

var speed: float:
	get:
		return attribute_component.get_attribute_value("speed") if attribute_component else 0.0

var hp: float:
	get:
		return attribute_component.get_attribute_value("hp") if attribute_component else 0.0
	set(value):
		if attribute_component:
			attribute_component.set_attribute_value("hp", value)

var max_hp: float:
	get:
		return attribute_component.get_attribute_value("max_hp") if attribute_component else 0.0

signal bullet_destroyed(bullet: EnemyBullet)


func _ready():
	hp = max_hp
	# 获取屏幕边界
	var viewport = get_viewport()
	if viewport:
		screen_bounds = Rect2(Vector2.ZERO, viewport.get_visible_rect().size)
	else:
		# 默认屏幕大小
		screen_bounds = Rect2(0, 0, 720, 1280)

	# 扩展边界以避免过早销毁
	screen_bounds = screen_bounds.grow(50)


func _physics_process(delta) -> void:
	if not is_active:
		return

	# 移动子弹
	global_position += direction * speed * delta

	# 检查是否超出屏幕边界
	if not screen_bounds.has_point(global_position):
		destroy_bullet()


## 初始化子弹
func init(start_position: Vector2, target_direction: Vector2):
	global_position = start_position
	direction = target_direction.normalized()
	is_active = true

	# 设置子弹朝向
	if direction != Vector2.ZERO:
		rotation = direction.angle() + PI/2  # 调整角度使子弹正确朝向


## 销毁子弹
func destroy_bullet() -> void:
	if not is_active:
		return

	is_active = false
	bullet_destroyed.emit(self)
	queue_free()


## 获取子弹当前伤害值（用于调试）
func get_current_damage() -> float:
	return damage


## 获取子弹当前速度（用于调试）
func get_current_speed() -> float:
	return speed


func _on_area_entered(area: Area2D) -> void:
	if not is_active:
		return

	# 检查是否碰撞玩家
	if area is PlayerManager:
		# 对玩家造成伤害
		area.take_damage(damage, Color.RED, self)
		destroy_bullet()
	elif area.name == "GhostDamageArea":
		var ball_damage = area.get_parent().get_damage().damage
		hp -= ball_damage
		if hp <= 0:
			destroy_bullet()


func _on_body_entered(body: Node2D) -> void:
	if not is_active:
		return
	if body is BallBase:
		var ball_damage = body.get_damage().damage
		hp -= ball_damage
		if hp <= 0:
			destroy_bullet()
