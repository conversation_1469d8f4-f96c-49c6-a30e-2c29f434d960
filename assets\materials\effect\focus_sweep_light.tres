[gd_resource type="ShaderMaterial" load_steps=2 format=3 uid="uid://cpccflgee6et7"]

[ext_resource type="Shader" uid="uid://dlcncsnxplcg2" path="res://assets/materials/shader/light_sweep.gdshader" id="1_yie1q"]

[resource]
shader = ExtResource("1_yie1q")
shader_parameter/sweep_speed = 3.0
shader_parameter/intensity = 1.0
shader_parameter/brightness = 0.9
shader_parameter/sweep_color = Color(1, 1, 1, 0.603922)
shader_parameter/direction = Vector2(-0.585, 2)
shader_parameter/sweep_interval = 1.0
shader_parameter/sweep_width = 1.0
shader_parameter/edge_sharpness = 0.8
