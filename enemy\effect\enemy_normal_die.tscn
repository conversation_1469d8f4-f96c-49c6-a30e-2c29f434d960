[gd_scene load_steps=18 format=3 uid="uid://dfir60jaky8t4"]

[ext_resource type="Script" uid="uid://daf3vb4ghs7kc" path="res://ball/effect/batch_particle_root_effect.gd" id="1_0y2cv"]
[ext_resource type="Shader" uid="uid://b72uivpctxxxy" path="res://assets/materials/shader/clip_black.gdshader" id="2_k3vos"]
[ext_resource type="Texture2D" uid="uid://cghyxu8emks11" path="res://assets/imgs/effects/50.png" id="3_hc7ar"]
[ext_resource type="Script" uid="uid://dcx66e75435fp" path="res://ball/effect/particle_root_effect.gd" id="4_ja6mt"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="5_1f72x"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_3xcsc"]
shader = ExtResource("2_k3vos")

[sub_resource type="Curve" id="Curve_opxv8"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(0.631148, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -1.7983, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_ko7l2"]
curve = SubResource("Curve_opxv8")

[sub_resource type="Gradient" id="Gradient_0y2cv"]
colors = PackedColorArray(0.597176, 0.597176, 0.597176, 1, 1, 1, 1, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_k3vos"]
gradient = SubResource("Gradient_0y2cv")

[sub_resource type="Curve" id="Curve_hc7ar"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.0936525), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_ja6mt"]
curve = SubResource("Curve_hc7ar")

[sub_resource type="Curve" id="Curve_1mj06"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.125, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.519771), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_qnp70"]
curve = SubResource("Curve_1mj06")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_7nwkf"]
resource_local_to_scene = true
lifetime_randomness = 1.0
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(100, 40, 1)
angle_min = 1.07288e-05
angle_max = 360.0
direction = Vector3(0, 0, 0)
spread = 180.0
initial_velocity_min = 10.0
initial_velocity_max = 40.0
gravity = Vector3(0, 0, 0)
scale_min = 0.1
scale_max = 0.2
scale_curve = SubResource("CurveTexture_qnp70")
color = Color(0.286967, 0.268934, 0.27134, 1)
color_ramp = SubResource("GradientTexture1D_k3vos")
alpha_curve = SubResource("CurveTexture_ko7l2")
emission_curve = SubResource("CurveTexture_ja6mt")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_odrhe"]
resource_local_to_scene = true
particle_flag_disable_z = true
angle_min = 1.07288e-05
angle_max = 360.0
direction = Vector3(0, 0, 0)
spread = 0.0
gravity = Vector3(0, 0, 0)
scale_max = 1.2
scale_curve = SubResource("CurveTexture_qnp70")
color = Color(1.1, 1.1, 1.1, 0.62)
alpha_curve = SubResource("CurveTexture_ko7l2")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_xxcoq"]
resource_local_to_scene = true
lifetime_randomness = 0.5
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(100, 40, 1)
angle_min = 1.07288e-05
angle_max = 360.0
direction = Vector3(0, -1, 0)
spread = 30.0
initial_velocity_min = 300.0
initial_velocity_max = 500.0
gravity = Vector3(0, 900, 0)
scale_min = 5.0
scale_max = 20.0
scale_curve = SubResource("CurveTexture_qnp70")
color = Color(0.113889, 0.0798955, 6.01634e-09, 1)
alpha_curve = SubResource("CurveTexture_ko7l2")

[node name="EnemyNormalDie" type="Node2D"]
z_index = -1
script = ExtResource("1_0y2cv")

[node name="Smoke" type="GPUParticles2D" parent="."]
material = SubResource("ShaderMaterial_3xcsc")
scale = Vector2(0.8, 0.8)
emitting = false
amount = 30
texture = ExtResource("3_hc7ar")
lifetime = 1.8
one_shot = true
explosiveness = 1.0
randomness = 1.0
collision_base_size = 10.0
process_material = SubResource("ParticleProcessMaterial_7nwkf")
script = ExtResource("4_ja6mt")

[node name="Circle" type="GPUParticles2D" parent="."]
emitting = false
amount = 1
texture = ExtResource("5_1f72x")
lifetime = 0.1
one_shot = true
explosiveness = 1.0
randomness = 1.0
collision_base_size = 10.0
process_material = SubResource("ParticleProcessMaterial_odrhe")
script = ExtResource("4_ja6mt")

[node name="Splinter" type="GPUParticles2D" parent="."]
emitting = false
amount = 20
lifetime = 0.8
one_shot = true
explosiveness = 1.0
randomness = 1.0
collision_base_size = 10.0
process_material = SubResource("ParticleProcessMaterial_xxcoq")
script = ExtResource("4_ja6mt")
