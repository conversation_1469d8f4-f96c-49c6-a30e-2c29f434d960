class_name BloodAbility
extends Ball<PERSON>bilityBase

@export var debuff_template: AttributeBuffDOT
var actual_debuff: AttributeBuffDOT

func _ready() -> void:
	actual_debuff = debuff_template.duplicate_buff()

var level: int:
	get:
		return actual_debuff.get_attribute_value("level") as int
	set(new_level):
		actual_debuff.set_attribute_value("level", new_level)

# 当弹球命中敌人时触发
func on_primary_hit(source: BallBase, target: EnemyBase) -> void:
	_apply_blood(target)

func on_secondary_hit(source: BallBase, target: EnemyBase, source_ability: BallAbilityBase) -> void:
	_apply_blood(target)

func _apply_blood(target: EnemyBase) -> void:
	if actual_debuff == null:
		push_warning("actual_debuff is not set in BloodAbility")
		return
	if target.has_method("add_buff"):
		target.add_buff(actual_debuff)


func get_attribute_set() -> AttributeSet:
	if is_instance_valid(actual_debuff):
		return actual_debuff.attribute_set
	return null
