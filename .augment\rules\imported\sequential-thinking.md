---
type: "agent_requested"
description: "深度思考"
---
## Sequential Thinking（分步问题解决框架）

请使用 [Sequential Thinking](https://github.com/smithery-ai/reference-servers/tree/main/src/sequentialthinking) 工具，分步骤指导解决复杂或开放式问题。每一步需结构化输出，便于追踪、回溯与分支探索。

### 使用规范

1. **任务拆解**：将整体任务拆分为若干“思维步骤”（thought），每步聚焦一个明确目标或假设。
2. **步骤结构**：
   - **目标/假设**：本步要解决什么？（如“调研身份验证方案”、“重构状态管理”）
   - **工具选择**：基于上下文，调用合适的 MCP 工具（如 `search_docs`、`code_generator`、`error_explainer` 等）。
   - **结果记录**：清晰记录本步输出、发现、结论或遇到的问题。
   - **下一步规划**：明确下一个思考目标或待解决子问题。
3. **不确定性处理**：
   - 可“分支思考”——对同一问题探索多种解决路径，分别记录、对比。
   - 支持回滚、修订前序思维步骤，动态调整思路。
   - 对不同策略、方案进行权衡分析。
4. **元数据要求**：
   - `thought`：当前思维内容（本步详细描述）
   - `thoughtNumber`：当前步骤编号
   - `totalThoughts`：预计总步骤数（可动态调整）

### 输出格式建议（Markdown）
Step {thoughtNumber}/{totalThoughts}
- **目标/假设**：...
- **工具选择**：...
- **结果/输出**：...
- **下一步**：...

存在不确定性时，使用分支思考探索多种解决路径，分分支思考请以“分支A/B/C”分别展开，并在后续步骤中可合流或对比。
