---
description: 属性与Buff系统实现原理
globs: 
alwaysApply: false
---
---
# 属性与Buff系统实现原理

## 整体架构
- 采用组件化设计，通过AttributeComponent向游戏对象提供属性管理能力
- 核心类结构：AttributeComponent → AttributeSet → Attribute → AttributeBuff
- 支持属性间依赖关系和拓扑排序计算，避免循环依赖
- 使用弱引用(WeakRef)避免循环引用导致的内存泄漏

## 属性系统特性
- 支持属性缓存机制，提高性能的同时保证数据一致性
- 实现脏标记(Dirty Flag)模式，只在属性变更时重新计算
- 提供派生属性机制，可基于其他属性自定义计算公式
- 支持属性值变更通知，实现响应式更新

## Buff系统设计
- 基于类型层次结构：AttributeBuff → AttributeBuffCount/AttributeBuffDOT
- 支持多种操作类型：加法、减法、乘法、除法、百分比和直接设置
- 提供灵活的持续时间策略：无限持续、定时过期、次数消耗
- 实现可叠加层数控制和多种持续时间合并策略

## Buff效果实现
- AttributeBuffCount：基于使用次数的buff，如暴击、闪避等临时效果
- AttributeBuffDOT：周期性生效的持续效果，如燃烧、中毒等状态
- 支持buff添加/移除事件，便于视觉特效和游戏逻辑联动
- 通过buff_name实现同类buff的识别和合并处理

## 性能优化
- 通过缓存系统避免频繁计算，特别是对于多层级依赖的属性
- 使用拓扑排序确保属性初始化和更新的正确顺序
- 仅在必要时才触发属性重计算，提高运行效率
- 自动清理过期buff，维持系统整洁

## 应用案例
- 敌人和玩家的血量、伤害、速度等核心属性管理
- 实现各类状态效果，如燃烧、中毒、流血等buff
- 支持连锁反应，如中毒效果可能触发伤害扩散到附近敌人
- 处理临时增益和减益效果，如暴击加成、减速等


