@tool
class_name AttributeBuffDOT extends AttributeBuff

signal dot_triggered(attribute: Attribute, buff: AttributeBuff)

## DOT的周期
var period: float
var _initialized: bool = false


var cycle_time: float = 0.0

func _init():
	super._init()
	if not attribute_set.has_attribute("period"):
		attribute_set.add_attribute("period", Attribute.create(1.0))


func run_process(delta: float):
	if is_pending_remove:
		return

	_try_to_trigger_dot(delta)
	super.run_process(delta)

func _try_to_trigger_dot(delta: float):
	if not _initialized:
		period = attribute_set.find_attribute("period").get_value()
		_initialized = true
		
	cycle_time += delta
	if cycle_time >= period:
		cycle_time = fmod(cycle_time, period)
		apply_to_attribute()
		return
	
	# 特殊情况：buff即将到期时的最后一次DOT检查
	# 如果buff即将到期，并且剩余的cycle_time + remaining_time能够完整地覆盖一个period，
	# 说明应该触发最后一次DOT
	if has_duration() and remaining_time <= delta:
		# 计算从上次DOT触发到buff完全到期的总时间
		var time_since_last_dot = cycle_time
		var time_until_expiry = remaining_time
		var total_available_time = time_since_last_dot + time_until_expiry
		
		# 如果总时间足够触发DOT，则触发
		if total_available_time >= period:
			apply_to_attribute()


func apply_to_attribute():
	if is_instance_valid(applied_attribute):
		applied_attribute.apply_buff_operation(self)
		dot_triggered.emit(applied_attribute, self)


func get_attribute_value(attribute_name: String) -> float:
	return attribute_set.find_attribute(attribute_name).get_value()


func set_attribute_value(attribute_name: String, value: float):
	attribute_set.find_attribute(attribute_name).set_value(value)
