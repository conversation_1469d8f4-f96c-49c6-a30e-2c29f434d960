[gd_scene load_steps=43 format=3 uid="uid://lo3iw104cq13"]

[ext_resource type="PhysicsMaterial" uid="uid://btxphd0oijm3l" path="res://assets/materials/physics/enemy.tres" id="1_awnsg"]
[ext_resource type="Script" uid="uid://bppfk7e8ywv0n" path="res://enemy/enemy_base.gd" id="2_444oo"]
[ext_resource type="Script" uid="uid://bfvxq0i61k3dm" path="res://ball/effect/buff_handlers/buff_handler_base.gd" id="3_fu5ju"]
[ext_resource type="Material" uid="uid://clg67pm3b1u8i" path="res://ball/effect/blood_effect/blood_debuff_material.tres" id="4_fuxja"]
[ext_resource type="Material" uid="uid://tgj0kdb4mduw" path="res://ball/effect/blood_effect/blood_to_die_material.tres" id="5_awnsg"]
[ext_resource type="Script" uid="uid://c05qkjgta4083" path="res://ball/effect/buff_handlers/blood_effect_handler.gd" id="6_444oo"]
[ext_resource type="Shader" uid="uid://c1qpfaitcf7lw" path="res://assets/materials/shader/noise_cover.gdshader" id="7_mmx4f"]
[ext_resource type="Material" uid="uid://x372txrlt4xk" path="res://ball/effect/burn_effect/burn_to_die_material.tres" id="8_i5tgl"]
[ext_resource type="Script" uid="uid://b5lii3xn3bsgk" path="res://ball/effect/buff_handlers/burn_effect_handler.gd" id="9_0thn0"]
[ext_resource type="Shader" uid="uid://brjwypq2onoc5" path="res://assets/materials/shader/hit_flash.tres" id="9_w2eak"]
[ext_resource type="PackedScene" uid="uid://u61jb1nfuges" path="res://ball/effect/poison_effect/poison_particle.tscn" id="11_gtnkc"]
[ext_resource type="Material" uid="uid://bjg14of7jmmu0" path="res://ball/effect/poison_effect/poison_to_die_material.tres" id="12_c8kox"]
[ext_resource type="Script" uid="uid://hxqw37dyx52" path="res://ball/effect/buff_handlers/poison_effect_handler.gd" id="13_dh3bi"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="15_c8kox"]
[ext_resource type="Script" uid="uid://boyhjsm72rmvx" path="res://addons/shader_sprite/shaders_sprite_2d.gd" id="15_eayir"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="18_5vjc6"]
[ext_resource type="PackedScene" uid="uid://cqjbmotilkpv8" path="res://enemy/prefab/enemy_vis.tscn" id="19_5vulm"]
[ext_resource type="Script" uid="uid://ucf5nur2irtk" path="res://attribute/scored_attribute.gd" id="20_ivojj"]
[ext_resource type="Script" uid="uid://dvjh7sib4d3po" path="res://attribute/hp_attribute.gd" id="21_1afk0"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="22_nou54"]
[ext_resource type="Material" uid="uid://cmrqqbs5mptgt" path="res://ball/effect/poison_effect/poison_debuff_material.tres" id="22_xbwb8"]

[sub_resource type="Resource" id="Resource_c8kox"]
resource_local_to_scene = true
script = ExtResource("6_444oo")
blood_material = ExtResource("4_fuxja")
blood_to_die_material = ExtResource("5_awnsg")
damage_color = Color(0.61, 0, 0, 1)
handled_buff_name = "blood"
metadata/_custom_type_script = "uid://c05qkjgta4083"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_82pk2"]
noise_type = 0
frequency = 0.0044
fractal_octaves = 10
fractal_lacunarity = 2.44

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_0trat"]
width = 1027
seamless = true
noise = SubResource("FastNoiseLite_82pk2")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_5o5ap"]
noise_type = 2
frequency = 0.0093
fractal_octaves = 1
fractal_lacunarity = 1.225
fractal_gain = 0.12

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_xs2cu"]
width = 927
seamless = true
noise = SubResource("FastNoiseLite_5o5ap")

[sub_resource type="Gradient" id="Gradient_j0tcx"]
offsets = PackedFloat32Array(0, 0.270408, 0.442543, 0.755501)
colors = PackedColorArray(0, 0, 0, 0, 0, 0, 0, 1, 1.3, 0.5, 0, 1, 3, 1.1, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_xbwb8"]
resource_local_to_scene = true
gradient = SubResource("Gradient_j0tcx")
use_hdr = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_odnwd"]
resource_local_to_scene = true
shader = ExtResource("7_mmx4f")
shader_parameter/noise_pattern = SubResource("NoiseTexture2D_0trat")
shader_parameter/noise_pattern2 = SubResource("NoiseTexture2D_xs2cu")
shader_parameter/scroll = Vector2(0, 0.43)
shader_parameter/scrol2 = Vector2(0, 0.765)
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_xbwb8")
shader_parameter/color_intensity = 2.0
shader_parameter/threshold = 0.273
shader_parameter/noise_alpha = 1.0

[sub_resource type="Resource" id="Resource_f8cjy"]
resource_local_to_scene = true
script = ExtResource("9_0thn0")
burn_material = SubResource("ShaderMaterial_odnwd")
burn_to_die_material = ExtResource("8_i5tgl")
damage_color = Color(1, 0.564706, 0, 1)
handled_buff_name = "burn"
metadata/_custom_type_script = "uid://b5lii3xn3bsgk"

[sub_resource type="Resource" id="Resource_drv11"]
resource_local_to_scene = true
script = ExtResource("13_dh3bi")
poison_material = ExtResource("22_xbwb8")
poison_to_die_material = ExtResource("12_c8kox")
poison_particle = ExtResource("11_gtnkc")
damage_color = Color(0.498039, 1, 0.0901961, 1)
splash_radius = 300.0
splash_probability = 0.3
splash_on_dot = true
splash_on_death = true
handled_buff_name = "poison"
metadata/_custom_type_script = "uid://bfvxq0i61k3dm"

[sub_resource type="Resource" id="Resource_ljufv"]
script = ExtResource("20_ivojj")
growth_per_score = 0.01
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_m807u"]
script = ExtResource("21_1afk0")
base_value = 100.0
can_cache = true
metadata/_custom_type_script = "uid://dvjh7sib4d3po"

[sub_resource type="Resource" id="Resource_stq6y"]
script = ExtResource("20_ivojj")
growth_per_score = 0.1
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 100.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_dfdlw"]
script = ExtResource("20_ivojj")
growth_per_score = 0.001
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_qpnmr"]
script = ExtResource("20_ivojj")
growth_per_score = 0.001
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 20.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_dmbdr"]
resource_local_to_scene = true
script = ExtResource("22_nou54")
attributes = Dictionary[StringName, ExtResource("15_c8kox")]({
&"damage": SubResource("Resource_ljufv"),
&"hp": SubResource("Resource_m807u"),
&"max_hp": SubResource("Resource_stq6y"),
&"score": SubResource("Resource_dfdlw"),
&"speed": SubResource("Resource_qpnmr")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[sub_resource type="ShaderMaterial" id="ShaderMaterial_nlq60"]
shader = ExtResource("9_w2eak")
shader_parameter/hit_color = Color(1, 1, 1, 1)
shader_parameter/rate = 0.0

[sub_resource type="ViewportTexture" id="ViewportTexture_awnsg"]
viewport_path = NodePath("SubViewport")

[sub_resource type="Animation" id="Animation_w2eak"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}

[sub_resource type="Animation" id="Animation_enx8a"]
resource_name = "default"
length = 0.66667
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.166667, 0.333333, 0.5, 0.666667),
"transitions": PackedFloat32Array(1, 2, 1, 2, 1),
"update": 0,
"values": [Vector2(0, 0), Vector2(0, 1), Vector2(0, 0), Vector2(0, -1), Vector2(0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_nlq60"]
_data = {
&"RESET": SubResource("Animation_w2eak"),
&"default": SubResource("Animation_enx8a")
}

[node name="Enemy" type="AnimatableBody2D"]
collision_layer = 4
collision_mask = 2
physics_material_override = ExtResource("1_awnsg")
sync_to_physics = false
script = ExtResource("2_444oo")
buff_handlers = Array[ExtResource("3_fu5ju")]([SubResource("Resource_c8kox"), SubResource("Resource_f8cjy"), SubResource("Resource_drv11")])

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("18_5vjc6")
attribute_set = SubResource("Resource_dmbdr")

[node name="SubViewport" type="SubViewport" parent="."]
disable_3d = true
transparent_bg = true
canvas_item_default_texture_repeat = 1
size = Vector2i(349, 240)

[node name="EnemyVis" parent="SubViewport" instance=ExtResource("19_5vulm")]
unique_name_in_owner = true
position = Vector2(170.667, 140)

[node name="Shadow" type="Node2D" parent="."]
modulate = Color(0, 0, 0, 0.345098)

[node name="@Polygon2D@77873" type="Polygon2D" parent="Shadow"]
polygon = PackedVector2Array(129.467, -34, 127.8, 26, -32.2, 26, -33.8667, 86, -113.867, 86, -124.667, 50, -121.333, -70, 118.667, -70)

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
material = SubResource("ShaderMaterial_nlq60")
script = ExtResource("15_eayir")
shaders_texture = SubResource("ViewportTexture_awnsg")
shaders_dic = Dictionary[StringName, Material]({
&"hit_flash": SubResource("ShaderMaterial_nlq60")
})

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_nlq60")
}
playback_auto_capture_ease_type = 2

[node name="@CollisionPolygon2D@77872" type="CollisionPolygon2D" parent="."]
polygon = PackedVector2Array(121.667, -60, 120, 0, -40, 0, -41.6667, 60, -121.667, 60, -124.667, 50, -121.333, -70, 118.667, -70)
