extends Control
class_name LevelUpPanel

## 升级弹窗面板
##
## 负责显示玩家升级时的选择界面，包括：
## - 当前拥有的弹球和遗物展示
## - 升级选项的显示和选择
## - 物品详细信息的展示
## - 升级确认功能

# 信号定义
signal upgrade_selected(upgrade_data: UIItemDataBase)
signal panel_closed()
# UI节点引用
@onready var title_label: Label = $MainPanel/MainContainer/VBoxContainer/TitleSection/TitleLabel
@onready var level_label: Label = $MainPanel/MainContainer/VBoxContainer/TitleSection/LevelLabel
@onready var balls_title: Label = $MainPanel/MainContainer/VBoxContainer/BallsSection/BallsTitle
@onready var balls_grid: GridContainer = $MainPanel/MainContainer/VBoxContainer/BallsSection/BallsGridContainer/BallsGrid
@onready var relics_title: Label = $MainPanel/MainContainer/VBoxContainer/RelicsSection/RelicsTitle
@onready var relics_grid: GridContainer = $MainPanel/MainContainer/VBoxContainer/RelicsSection/RelicsGridContainer/RelicsGrid
@onready var effect_description: RichTextLabel = $MainPanel/MainContainer/VBoxContainer/EffectDescriptionSection/EffectDescriptionMargin/EffectDescriptionLabel
@onready var upgrade_options_grid: GridContainer = $MainPanel/MainContainer/VBoxContainer/UpgradeOptionsSection/UpgradeOptionsGridContainer/UpgradeOptionsGrid
@onready var confirm_button: Button = $MainPanel/MainContainer/VBoxContainer/ConfirmSection/ConfirmButton

# 数据存储
var current_balls: Array[UIItemDataBase] = []
var current_relics: Array[UIItemDataBase] = []
var upgrade_options: Array[UIItemDataBase] = []
var selected_upgrade_index: int = -1

# 选择状态管理
enum SelectionType { NONE, BALL_SLOT, RELIC_SLOT, UPGRADE_OPTION }
var current_selection_type: SelectionType = SelectionType.NONE
var current_selection_index: int = -1
# 物品槽预制场景
const ITEM_SLOT_SCENE: PackedScene = preload("res://ui/prefab/item_slot.tscn")
const UPGRADE_OPTION_SCENE: PackedScene = preload("res://ui/prefab/upgrade_option.tscn")
# 常量定义
const MAX_BALLS: int = 8
const MAX_RELICS: int = 8
const LEVEL_UP_OPTIONS: int = 3


func _ready() -> void:
	# 连接确认按钮信号
	confirm_button.pressed.connect(_on_confirm_button_pressed)

	# 初始化物品槽
	_initialize_item_slots()

	# 设置初始状态
	_reset_effect_description()

	# 设置暂停模式以确保在游戏暂停时仍能接收输入
	process_mode = Node.PROCESS_MODE_WHEN_PAUSED


## 显示升级面板
## @param level_data: 包含当前等级信息的字典
## @param balls_data: 当前拥有的弹球数据数组
## @param relics_data: 当前拥有的遗物数据数组
## @param options_data: 升级选项数据数组
func show_panel(level_data: UIItemDataBase, balls_data: Array, relics_data: Array, options_data: Array) -> void:
	# 更新等级信息
	_update_level_info(level_data)

	# 更新物品数据（类型转换）
	current_balls.clear()
	for ball in balls_data:
		current_balls.append(ball)

	current_relics.clear()
	for relic in relics_data:
		current_relics.append(relic)

	upgrade_options.clear()
	for option in options_data:
		upgrade_options.append(option)

	# 更新UI显示
	_update_balls_display()
	_update_relics_display()
	_update_upgrade_options()

	# 重置选择状态
	selected_upgrade_index = -1
	current_selection_type = SelectionType.NONE
	current_selection_index = -1
	_update_confirm_button()
	_reset_effect_description()

	# 暂停游戏
	get_tree().paused = true

	# 使用GuiTransitions系统显示面板
	GuiTransitions.show("LevelUpPanel")
	await GuiTransitions.show_completed


## 隐藏升级面板
func hide_panel() -> void:
	GuiTransitions.hide("LevelUpPanel")
	await GuiTransitions.hide_completed

	# 恢复游戏
	get_tree().paused = false

	# 发送面板关闭信号
	panel_closed.emit()


## 初始化物品槽
func _initialize_item_slots() -> void:

	# 创建弹球槽位
	for i in range(MAX_BALLS):
		var ball_slot: ItemSlot = _create_item_slot()
		ball_slot.item_clicked.connect(func(item_data: UIItemDataBase): _on_ball_slot_clicked(i, item_data))
		balls_grid.add_child(ball_slot)

	# 创建遗物槽位
	for i in range(MAX_RELICS):
		var relic_slot: ItemSlot = _create_item_slot()
		relic_slot.item_clicked.connect(func(item_data: UIItemDataBase): _on_relic_slot_clicked(i, item_data))
		relics_grid.add_child(relic_slot)

	# 创建升级选项
	for i in range(LEVEL_UP_OPTIONS):
		var option_button: UpgradeOption = _create_upgrade_option()
		upgrade_options_grid.add_child(option_button)


## 创建物品槽
func _create_item_slot() -> ItemSlot:
	var slot: ItemSlot = ITEM_SLOT_SCENE.instantiate()
	return slot


func _create_upgrade_option() -> UpgradeOption:
	var option_button: UpgradeOption = UPGRADE_OPTION_SCENE.instantiate()

	# 在创建时就连接信号，避免后续重复连接导致的性能问题
	option_button.option_selected.connect(_on_upgrade_option_selected)

	return option_button


## 更新等级信息显示
func _update_level_info(level_data: UIItemDataBase) -> void:
	var current_level = level_data.get("current_level")
	var next_level = level_data.get("next_level")
	level_label.text = "等级 %d → %d" % [current_level, next_level]


## 更新弹球显示
func _update_balls_display() -> void:
	balls_title.text = "☄️ 拥有弹球 (%d/%d)" % [current_balls.size(), MAX_BALLS]

	# 更新每个弹球槽位
	for i in range(MAX_BALLS):
		var slot: ItemSlot = balls_grid.get_child(i) as ItemSlot
		if i < current_balls.size():
			var ball_data: UIItemDataBase = current_balls[i]
			slot.set_item_data(ball_data)
		else:
			slot.set_empty()


## 更新遗物显示
func _update_relics_display() -> void:
	relics_title.text = "⚜️ 拥有遗物 (%d/%d)" % [current_relics.size(), MAX_RELICS]

	# 更新每个遗物槽位
	for i in range(MAX_RELICS):
		var slot: ItemSlot = relics_grid.get_child(i) as ItemSlot
		if i < current_relics.size():
			var relic_data: UIItemDataBase = current_relics[i]
			slot.set_item_data(relic_data)
		else:
			slot.set_empty()


## 更新升级选项显示
func _update_upgrade_options() -> void:
	# 更新升级选项数据（信号已在创建时连接，无需重复连接）
	for i in range(min(upgrade_options.size(), upgrade_options_grid.get_child_count())):
		var option_button: UpgradeOption = upgrade_options_grid.get_child(i)
		var option_data: UIItemDataBase = upgrade_options[i]
		option_button.set_option_data(option_data, i)
		option_button.visible = true

	# 隐藏多余的按钮
	for i in range(upgrade_options.size(), upgrade_options_grid.get_child_count()):
		var option_button: UpgradeOption = upgrade_options_grid.get_child(i)
		option_button.visible = false


## 统一的选择状态管理
## @param selection_type: 选择类型（弹球槽位、遗物槽位或升级选项）
## @param index: 选择的索引
## @param item_data: 选择的物品数据
func _set_selection(selection_type: SelectionType, index: int, item_data: UIItemDataBase) -> void:
	# 清除之前的选择状态
	_clear_all_selections()

	# 设置新的选择状态
	current_selection_type = selection_type
	current_selection_index = index

	# 根据选择类型设置对应UI元素的选中状态
	match selection_type:
		SelectionType.BALL_SLOT:
			if index < balls_grid.get_child_count():
				var slot: ItemSlot = balls_grid.get_child(index) as ItemSlot
				slot.set_selected(true)
				_show_item_description(item_data.description)
		SelectionType.RELIC_SLOT:
			if index < relics_grid.get_child_count():
				var slot: ItemSlot = relics_grid.get_child(index) as ItemSlot
				slot.set_selected(true)
				_show_item_description(item_data.description)
		SelectionType.UPGRADE_OPTION:
			selected_upgrade_index = index
			if index < upgrade_options_grid.get_child_count():
				var option: UpgradeOption = upgrade_options_grid.get_child(index) as UpgradeOption
				option.set_selected(true)
				_show_item_description(item_data.description_level_up if item_data.is_upgrade_option else item_data.description)

	# 更新确认按钮状态
	_update_confirm_button()


## 清除所有选择状态
func _clear_all_selections() -> void:
	# 清除弹球槽位选择状态
	for i in range(balls_grid.get_child_count()):
		var slot: ItemSlot = balls_grid.get_child(i) as ItemSlot
		slot.set_selected(false)

	# 清除遗物槽位选择状态
	for i in range(relics_grid.get_child_count()):
		var slot: ItemSlot = relics_grid.get_child(i) as ItemSlot
		slot.set_selected(false)

	# 清除升级选项选择状态
	for i in range(upgrade_options_grid.get_child_count()):
		var option: UpgradeOption = upgrade_options_grid.get_child(i) as UpgradeOption
		option.set_selected(false)

	# 重置选择状态变量
	current_selection_type = SelectionType.NONE
	current_selection_index = -1
	selected_upgrade_index = -1


## 弹球槽位点击处理
func _on_ball_slot_clicked(index: int, item_data: UIItemDataBase) -> void:
	_set_selection(SelectionType.BALL_SLOT, index, item_data)


## 遗物槽位点击处理
func _on_relic_slot_clicked(index: int, item_data: UIItemDataBase) -> void:
	_set_selection(SelectionType.RELIC_SLOT, index, item_data)


## 升级选项选择处理
func _on_upgrade_option_selected(option_data: UIItemDataBase, index: int) -> void:
	_set_selection(SelectionType.UPGRADE_OPTION, index, option_data)


## 显示物品描述
func _show_item_description(description: String) -> void:
	effect_description.clear()
	effect_description.append_text(description)


## 重置效果描述
func _reset_effect_description() -> void:
	effect_description.clear()
	effect_description.append_text("""
		[center][color=#999999][i]点击物品或升级选项查看详细说明[/i][/color][/center]
	""")


## 更新确认按钮状态
func _update_confirm_button() -> void:
	if selected_upgrade_index >= 0:
		confirm_button.disabled = false
		confirm_button.text = "确认升级"
	else:
		confirm_button.disabled = true
		confirm_button.text = "请先选择一个升级选项"


## 确认按钮点击处理
func _on_confirm_button_pressed() -> void:
	if selected_upgrade_index >= 0 and selected_upgrade_index < upgrade_options.size():
		var selected_option: UIItemDataBase = upgrade_options[selected_upgrade_index]

		# 发送升级选择信号
		upgrade_selected.emit(selected_option)

		# 隐藏面板
		hide_panel()
	else:
		push_error("无效的升级选择：选择索引超出范围或未选择任何选项")


## 获取示例数据（用于测试）
func get_sample_data() -> Dictionary:
	return {
		"level_data": {
			"current_level": 5,
			"next_level": 6
		},
		"balls_data": [
			{"name": "基础弹球", "icon": "⚽", "description": "最基础的攻击弹球", "effects": ["伤害: 10", "速度: 100"]},
			{"name": "火焰弹球", "icon": "🔥", "description": "造成火焰伤害的弹球", "effects": ["伤害: 15", "附加燃烧效果"]},
			{"name": "冰霜弹球", "icon": "❄️", "description": "造成冰霜伤害的弹球", "effects": ["伤害: 12", "附加减速效果"]}
		],
		"relics_data": [
			{"name": "力量护符", "icon": "💪", "description": "增强攻击力的护符", "effects": ["攻击力 +20%"]},
			{"name": "速度之靴", "icon": "👟", "description": "提升移动速度的靴子", "effects": ["移动速度 +15%"]}
		],
		"options_data": [
			{"name": "雷电弹球", "icon": "⚡", "description": "获得新的雷电弹球", "effects": ["伤害: 18", "链式闪电攻击"]},
			{"name": "火焰强化", "icon": "🔥+", "description": "升级现有火焰弹球", "effects": ["伤害: 15 → 25", "燃烧范围扩大"]},
			{"name": "幸运硬币", "icon": "🪙", "description": "获得幸运硬币遗物", "effects": ["金币获得 +50%", "暴击率 +10%"]}
		]
	}
