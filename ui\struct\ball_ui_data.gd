class_name BallUIData
extends UIItemDataBase

## 弹球UI数据类
##
## 用于在升级面板中显示弹球信息的数据结构
## 包含弹球的名称、描述、图标、属性等UI显示所需的所有信息

var inst_id: int = 0
## 弹球名称
var name: String = ""

## 弹球描述
var desc: String = ""
var desc_level_up: String = ""

## 弹球图标（emoji或符号）
var icon = null

## 弹球类型标识
var ball_type: String = ""

## 弹球等级
var level: int = 1

## 效果描述列表
var effects: Array[String] = []
var effects_level_up: Array[String] = []

## 弹球稀有度
var rarity_level: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON

var ball_scene: PackedScene = null

## 最终描述
var description: String:
	get:
		var text = "[font_size=40]%s[/font_size]\n等级 %s" % [name, level]
		if desc != "":
			text += "[font top=5]\n [/font]%s" % desc
		for effect in effects:
			text += "[font top=5]\n [/font]%s" % effect
		return text


var description_level_up: String:
	get:
		var text = "[font_size=40]%s[/font_size]\n等级 %s > %s" % [name, level - 1, level]
		if desc_level_up != "":
			text += "[font top=5]\n [/font]%s" % desc_level_up
		for effect in effects_level_up:
			text += "[font top=5]\n [/font]%s" % effect
		return text


## 构造函数
## @param p_name: 弹球名称
## @param p_description: 弹球描述
## @param p_icon: 弹球图标
## @param p_ball_type: 弹球类型
## @param p_level: 弹球等级
## @param p_effects: 效果描述列表
func _init(
	p_inst_id: int = 0,
	p_name: String = "",
	p_desc: String = "",
	p_desc_level_up: String = "",
	p_icon = "", # 可以是String、Texture2D或ShaderMaterial
	p_ball_type: String = "",
	p_level: int = 1,
	p_effects: Array[String] = [],
	p_effects_level_up: Array[String] = [],
	p_rarity: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON,
	p_ball_scene: PackedScene = null
):
	inst_id = p_inst_id
	name = p_name
	desc = p_desc
	desc_level_up = p_desc_level_up
	icon = p_icon
	ball_type = p_ball_type
	level = p_level
	effects = p_effects.duplicate() # 创建副本避免引用问题
	effects_level_up = p_effects_level_up.duplicate() # 创建副本避免引用问题
	rarity_level = p_rarity
	ball_scene = p_ball_scene

	
## 检查数据是否有效
## @return: 数据是否完整有效
func is_valid() -> bool:
	return name != "" and icon != null and ball_type != ""

## 获取简短的显示名称（用于UI显示）
## @return: 简短的显示名称
func get_display_name() -> String:
	if level > 1:
		return "%s Lv.%d" % [name, level]
	else:
		return name


## 创建数据的副本
## @return: 新的BallUIData实例
func create_copy() -> BallUIData:
	var new_data = BallUIData.new()
	new_data.inst_id = inst_id
	new_data.name = name
	new_data.desc = desc
	new_data.desc_level_up = desc_level_up
	new_data.icon = icon
	new_data.ball_type = ball_type
	new_data.level = level
	new_data.effects = effects.duplicate()
	new_data.effects_level_up = effects_level_up.duplicate()
	new_data.rarity_level = rarity_level
	new_data.ball_scene = ball_scene
	new_data.is_upgrade_option = is_upgrade_option  # 复制基类字段
	return new_data
