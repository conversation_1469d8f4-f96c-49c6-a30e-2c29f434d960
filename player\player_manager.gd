class_name PlayerManager
extends Area2D

@onready var muzzle = $Muzzle
@onready var attribute_component: AttributeComponent = $AttributeComponent
@onready var relic_manager: RelicManager = $RelicManager
@onready var animated_sprite_2d: AnimatedSprite2D = $AnimatedSprite2D

## 弹球池管理器引用（通过依赖注入设置）
var ball_pool_manager: BallPoolManager = null

@export_category("Ball System")
@export var ball_prefab: Dictionary = {
	"normal": load("res://ball/prefab/normal_ball.tscn"),
	"fire": load("res://ball/prefab/fire_ball.tscn"),
	"ghost": load("res://ball/prefab/ghost_ball.tscn"),
	"poison": load("res://ball/prefab/poison_ball.tscn"),
	"blood": load("res://ball/prefab/blood_ball.tscn"),
	"shock": load("res://ball/prefab/shock_ball.tscn"),
}

@export_category("Player")
@export var shoot_interval: float = 0.5
@export var initial_ball_count: int = 4
@export var move_speed: float = 300.0
@export var move_boundary: Rect2
@export var hit_force: float = 60.0
@export var hit_stagger_time: float = 0.4
@export var invincible_time: float = 0.5

var hit_stagger_timer: float = 0.0
var invincible_timer: float = 0.0
var move_direction: Vector2 = Vector2.ZERO

var hp:
	get:
		return attribute_component.get_attribute_value("hp")
	set(value):
		attribute_component.find_attribute("hp").set_value(value)

var max_hp:
	get:
		return attribute_component.get_attribute_value("max_hp")
	set(value):
		attribute_component.find_attribute("max_hp").set_value(value)

# 瞄准线
var aim_target_pos: Vector2 = Vector2.ZERO
# 触摸控制状态
var move_touch_index: int = -1
var aim_touch_index: int = -1
var move_start_pos: Vector2 = Vector2.ZERO
var player_start_pos: Vector2 = Vector2.ZERO
# 弹球
var ball_id_queue: Array = []
var shoot_timer: float = 0.0
var active_balls: Array = []
# tween
var attack_tween: Tween
var hit_tween: Tween
var overlapping_enemies: Array = []


func _ready():
	# 将玩家添加到player组，以便敌人可以找到
	add_to_group("player")

	# relic_manager.add_relic(preload("res://relic/relics/bottom_crit.tres"))  # 临时注释掉，避免类型错误
	# 弹球系统初始化将在依赖注入后进行
	set_process_input(true)


## 设置弹球池管理器依赖并初始化弹球系统
## @param p_ball_pool_manager: 弹球池管理器引用
func set_ball_pool_manager(p_ball_pool_manager: BallPoolManager) -> void:
	ball_pool_manager = p_ball_pool_manager

	# 现在可以安全地初始化弹球系统
	init_ball_system()


func _input(event: InputEvent):
	# PC端鼠标瞄准
	if event is InputEventMouseMotion:
		update_target_pos(event.position.x, event.position.y)

	if event is InputEventKey:
		handle_keyboard_movement()

	# 移动端触摸控制
	if event is InputEventScreenTouch or event is InputEventScreenDrag:
		handle_touch_event(event)


func handle_touch_event(event: InputEvent):
	var screen_height = get_viewport().get_visible_rect().size.y

	# 新的触摸开始
	if event is InputEventScreenTouch and event.pressed:
		# 屏幕下半部分用于移动
		if event.position.y > screen_height / 2 and move_touch_index == -1:
			move_touch_index = event.index
			move_start_pos = event.position
			player_start_pos = global_position
			# 屏幕上半部分用于瞄准
		elif event.position.y <= screen_height / 2 and aim_touch_index == -1:
			aim_touch_index = event.index
			update_target_pos(event.position.x, event.position.y)

		# 触摸释放
	elif event is InputEventScreenTouch and not event.pressed:
		if event.index == move_touch_index:
			move_touch_index = -1
			move_direction = Vector2.ZERO
		elif event.index == aim_touch_index:
			aim_touch_index = -1

	# 手指拖动
	elif event is InputEventScreenDrag:
		if event.index == move_touch_index:
			var offset = event.position - move_start_pos
			move_direction = offset.normalized()
		elif event.index == aim_touch_index:
			update_target_pos(event.position.x, event.position.y)


func handle_keyboard_movement():
	var move_vec = Input.get_vector("ui_left", "ui_right", "ui_up", "ui_down")
	move_direction = move_vec


func handle_move(delta: float) -> void:
	if hit_stagger_timer >= 0.0:
		return
	if move_direction.x != 0 or move_direction.y != 0:
		animated_sprite_2d.play("walk")
		global_position += move_direction * move_speed * delta
	else:
		animated_sprite_2d.play("idle")


func init_ball_system():
	if not ball_pool_manager:
		push_error("PlayerManager: BallPoolManager未注入，无法初始化弹球系统")
		return

	# 注册普通弹球预制体
	# for i in range(3):
	# ball_id_queue.append(ball_pool_manager.create_ball(ball_prefab["shock"]).get_instance_id())
	# ball_id_queue.append(ball_pool_manager.create_ball(ball_prefab["blood"]).get_instance_id())
	ball_id_queue.append(ball_pool_manager.create_ball(ball_prefab["poison"]).get_instance_id())
# ball_id_queue.append(ball_pool_manager.create_ball(ball_prefab["normal"]).get_instance_id())
# ball_id_queue.append(ball_pool_manager.create_ball(ball_prefab["ghost"]).get_instance_id())

func add_ball(ball_scene: PackedScene) -> void:
	ball_id_queue.append(ball_pool_manager.create_ball(ball_scene).get_instance_id())


func update_target_pos(x: float, y: float):
	aim_target_pos = Vector2(x, y)


func _process(delta):
	# 让炮口处理所有瞄准逻辑（旋转、画线）
	muzzle.update_aim(aim_target_pos)

	# 从炮口获取射击方向
	var dir = muzzle.get_current_dir()

	# 更新弹球发射
	update_ball_shooting(delta, muzzle.global_position, dir)
	# 清理已回收的弹球
	cleanup_active_balls()


func _physics_process(delta: float):
	if hit_stagger_timer >= 0.0:
		hit_stagger_timer -= delta
	if invincible_timer >= 0.0:
		invincible_timer -= delta
	# handle_keyboard_movement()
	handle_move(delta)

	# 从炮口获取射击方向
	var dir = muzzle.get_current_dir()

	# 更新弹球发射
	update_ball_shooting(delta, muzzle.global_position, dir)
	# 清理已回收的弹球
	cleanup_active_balls()

	# 将玩家位置限制在move_boundary内
	global_position.x = clamp(global_position.x, move_boundary.position.x, move_boundary.end.x)
	global_position.y = clamp(global_position.y, move_boundary.position.y, move_boundary.end.y)

	if hit_stagger_timer <= 0.0:
		if not overlapping_enemies.is_empty():
			var enemy = overlapping_enemies[0] # 只处理第一个接触的敌人
			if is_instance_valid(enemy):
				hit_stagger_timer = hit_stagger_time
				take_damage(enemy.get_damage(), Color.RED, enemy)
			else:
				# 如果实例无效（可能已被销毁），从列表中移除
				overlapping_enemies.pop_front()


func update_ball_shooting(delta, pos: Vector2, dir: Vector2):
	shoot_timer += delta
	if shoot_timer >= shoot_interval and ball_id_queue.size() > 0:
		shoot_ball(pos, dir)
		shoot_timer = 0


func shoot_ball(pos: Vector2, dir: Vector2) -> void:
	if not ball_pool_manager:
		push_error("PlayerManager: BallPoolManager未注入，无法发射弹球")
		return

	var ball_id = ball_id_queue.pop_front()
	if ball_id == null:
		return
	var ball = ball_pool_manager.get_ball(ball_id, dir, pos)
	if ball:
		play_attack_effect()
		active_balls.append(ball)
		if !ball.has_connections("reach_bottom"):
			ball.connect("reach_bottom", Callable(self, "on_ball_reach_bottom"))
	else:
		print("发射弹球失败")
		ball_id_queue.push_front(ball_id)


func on_ball_reach_bottom(ball):
	# recycle_ball(ball)
	track_and_move_ball(ball)


# 弹球追踪并向玩家移动
func track_and_move_ball(ball: BallBase) -> void:
	if not is_instance_valid(ball):
		return

	# 连接追踪到达信号
	if not ball.has_connections("tracking_arrived"):
		ball.connect("tracking_arrived", Callable(self, "on_ball_tracking_arrived"))

	# 开始追踪玩家
	ball.start_tracking(self)


# 弹球追踪到达处理
func on_ball_tracking_arrived(ball: BallBase) -> void:
	if not is_instance_valid(ball):
		return

	# 停止追踪并回收弹球
	ball.stop_tracking()
	recycle_ball(ball)


func recycle_ball(ball: BallBase):
	if not ball_pool_manager:
		push_error("PlayerManager: BallPoolManager未注入，无法回收弹球")
		return

	ball_pool_manager.recycle_ball(ball)
	if active_balls.has(ball):
		active_balls.erase(ball)
	ball_id_queue.append(ball.get_instance_id())


func take_damage(damage_amount: int, color: Color = Color.WHITE, source: Object = null) -> void:
	var final_damage = damage_amount * (1.0 - find_attribute("damage_reduction").get_value() / 100.0)
	if source is EnemyBase:
		knock_back(source)
		if invincible_timer > 0.0:
			return
		hp -= final_damage
		GameEvents.player_received_damage.emit(self, {
			"damage": final_damage,
			"remaining_hp": hp,
			"max_hp": max_hp,
		})
		play_hit_effect()
	elif source is EnemyBullet:
		if invincible_timer > 0.0:
			return
		hp -= final_damage
		GameEvents.player_received_damage.emit(self, {
			"damage": final_damage,
			"remaining_hp": hp,
			"max_hp": max_hp,
		})
		play_hit_effect()
	invincible_timer = invincible_time

## 播放子弹击中效果
func play_hit_effect() -> void:
	var _material: ShaderMaterial = animated_sprite_2d.material
	if not _material:
		print("错误：AnimatedSprite2D上没有找到ShaderMaterial！")
		return

	if hit_tween:
		hit_tween.kill()
	hit_tween = create_tween()
	hit_tween.tween_method(
		func(value: float):
			_material.set_shader_parameter("flash_modifier", value),
		0.0, 1.0, 0.1
	).set_ease(Tween.EASE_OUT)
	hit_tween.tween_method(
		func(value: float):
			_material.set_shader_parameter("flash_modifier", value),
		1.0, 0.0, 0.1
	).set_ease(Tween.EASE_IN)


func cleanup_active_balls():
	active_balls = active_balls.filter(func(ball): return ball and ball.get_parent())


func get_queue_size() -> int:
	return ball_id_queue.size()


func get_active_ball_count() -> int:
	return active_balls.size()


func get_attribute_value(attribute_name: String) -> float:
	return attribute_component.get_attribute_value(attribute_name)


func find_attribute(attribute_name: String) -> Attribute:
	return attribute_component.find_attribute(attribute_name)


func play_attack_effect() -> void:
	# 1. 获取材质
	var _material: ShaderMaterial = animated_sprite_2d.material as ShaderMaterial
	if not _material:
		print("错误：AnimatedSprite2D上没有找到ShaderMaterial！")
		return

	if attack_tween:
		attack_tween.kill()
	attack_tween = create_tween()
	attack_tween.tween_method(
		func(value: float):
			_material.set_shader_parameter("body_starts_at_y", value),
			0.5, 0.55, 0.08
	).set_ease(Tween.EASE_IN)
	attack_tween.tween_method(
		func(value: float):
			_material.set_shader_parameter("body_starts_at_y", value),
			0.55, 0.5, 0.08
	).set_ease(Tween.EASE_OUT)


func knock_back(enemy: EnemyBase) -> void:
	var dir = self.global_position - enemy.global_position
	var tween = create_tween()
	tween.tween_property(self, "global_position", self.global_position + dir.normalized() * hit_force, hit_stagger_time).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)


func _on_body_entered(body: Node2D) -> void:
	if body is EnemyBase:
		if not overlapping_enemies.has(body):
			overlapping_enemies.append(body)
	if body is BallBase:
		var dir = body.linear_velocity.normalized()
		# 如果是远离玩家方向
		if dir.dot(self.global_position - body.global_position) < 0:
			return
		recycle_ball(body)


func _on_body_exited(body: Node2D) -> void:
	if body is EnemyBase and overlapping_enemies.has(body):
		overlapping_enemies.erase(body)
