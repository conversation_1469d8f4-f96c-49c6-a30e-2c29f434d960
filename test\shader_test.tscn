[gd_scene load_steps=3 format=3 uid="uid://c6w6lwai5ufh8"]

[ext_resource type="Material" uid="uid://cy0hu1lsoauxy" path="res://assets/materials/fluid_outline_material.tres" id="1"]
[ext_resource type="Material" uid="uid://dnknq6dqvjkqv" path="res://assets/materials/ui_dynamic_border_material.tres" id="2"]

[node name="ShaderTest" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = 50.0
offset_right = -50.0
offset_bottom = -50.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "动态UI边框Shader测试"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="LeftPanel" type="VBoxContainer" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label1" type="Label" parent="VBoxContainer/HBoxContainer/LeftPanel"]
layout_mode = 2
text = "复杂流体边框效果 (fluid_outline.gdshader)"
horizontal_alignment = 1

[node name="FluidBorderRect" type="ColorRect" parent="VBoxContainer/HBoxContainer/LeftPanel"]
material = ExtResource("1")
layout_mode = 2
size_flags_vertical = 3
color = Color(0.2, 0.2, 0.8, 1)

[node name="VSeparator" type="VSeparator" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2

[node name="RightPanel" type="VBoxContainer" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label2" type="Label" parent="VBoxContainer/HBoxContainer/RightPanel"]
layout_mode = 2
text = "简化UI边框效果 (ui_dynamic_border.gdshader)"
horizontal_alignment = 1

[node name="UIBorderRect" type="ColorRect" parent="VBoxContainer/HBoxContainer/RightPanel"]
material = ExtResource("2")
layout_mode = 2
size_flags_vertical = 3
color = Color(0.8, 0.2, 0.2, 1)

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="Instructions" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "参数说明：
• border_width: 边框宽度
• animation_speed: 动画速度
• border_color: 边框颜色
• wave_frequency: 波浪频率 (仅UI版本)
• noise_intensity: 噪声强度
• enable_glow: 启用发光效果 (仅UI版本)
• color_shift_speed: 颜色变化速度 (仅UI版本)

在检查器中选择ColorRect节点，展开Material属性来调整参数"
