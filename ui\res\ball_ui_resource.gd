class_name BallUIResource
extends Resource

## 弹球UI资源类
##
## 用于配置弹球的UI显示信息，包括名称、描述、图标等
## 通过资源文件的方式管理，便于配置和本地化

## 弹球名称
@export var ball_name: String = "未命名弹球"

## 弹球描述（支持BBCode富文本和动态占位符）
@export_multiline var description: String = "这是一个弹球的描述。"

## 弹球升级描述（支持BBCode富文本和动态占位符）
@export_multiline var description_level_up: String = "这是一个弹球的升级描述。"

## 图标类型枚举
enum IconType {
	EMOJI,          ## emoji文本图标
	TEXTURE,        ## 静态图片
	SHADER_MATERIAL ## 着色器材质（游戏中的实际外观）
}

## 图标类型
@export var icon_type: IconType = IconType.EMOJI

## 弹球图标（emoji或符号）
@export var icon_text: String = "⚽"

## 图片图标
@export var icon_texture: Texture2D

## 着色器材质图标
@export var icon_shader_material: ShaderMaterial

## 弹球稀有度（新版本，使用全局枚举）
@export var rarity_level: GameConstants.RarityLevel = GameConstants.RarityLevel.COMMON

## 弹球权重（用于升级选项生成）
@export var rarity_weight: float = 10.0


## 获取稀有度对应的颜色（使用新的稀有度系统）
func get_rarity_color() -> Color:
	return GameConstants.get_rarity_color(rarity_level)

## 获取稀有度显示名称
func get_rarity_display_name() -> String:
	return GameConstants.get_rarity_display_name(rarity_level)

## 获取显示名称（包含稀有度信息）
func get_display_name() -> String:
	var rarity_name = get_rarity_display_name()
	if rarity_level != GameConstants.RarityLevel.COMMON:
		return "%s (%s)" % [ball_name, rarity_name]
	else:
		return ball_name

## 获取权重值（确保非负）
func get_weight() -> float:
	return max(0.0, rarity_weight)


## 设置权重值（确保非负）
func set_weight(new_weight: float) -> void:
	rarity_weight = max(0.0, new_weight)


## 获取当前图标（根据类型返回对应的图标）
func get_icon() -> Variant:
	match icon_type:
		IconType.EMOJI:
			return icon_text
		IconType.TEXTURE:
			return icon_texture
		IconType.SHADER_MATERIAL:
			return icon_shader_material
		_:
			return icon_text

## 获取图标的显示文本（用于UI显示）
func get_icon_display():
	match icon_type:
		IconType.EMOJI:
			return icon_text
		IconType.TEXTURE:
			return icon_texture
		IconType.SHADER_MATERIAL:
			return icon_shader_material
		_:
			return icon_text

## 检查资源是否有效
func is_valid() -> bool:
	if ball_name == "":
		return false

	match icon_type:
		IconType.EMOJI:
			return icon_text != ""
		IconType.TEXTURE:
			return icon_texture != null
		IconType.SHADER_MATERIAL:
			return icon_shader_material != null
		_:
			return icon_text != ""
