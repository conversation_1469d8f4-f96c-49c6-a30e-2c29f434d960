class_name FireAbility
extends Ball<PERSON>bilityBase

@export var debuff_template: AttributeBuffDOT
var actual_debuff: AttributeBuffDOT

func _ready() -> void:
	actual_debuff = debuff_template.duplicate_buff()

var level: int:
	get:
		return actual_debuff.get_attribute_value("level") as int
	set(new_level):
		actual_debuff.set_attribute_value("level", new_level)

# 当弹球直接命中一个敌人时调用 (主目标命中)。
func on_primary_hit(source: BallBase, target: EnemyBase) -> void:
	_apply_burn(target)


# 当被其他能力的效果波及，命中一个次要目标时调用。
func on_secondary_hit(source: BallBase, target: EnemyBase, source_ability: BallAbilityBase) -> void:
	_apply_burn(target)


func _apply_burn(target: EnemyBase) -> void:
	if actual_debuff == null:
		push_warning("actual_debuff is not set in FireAbility")
		return

	if target.has_method("add_buff"):
		target.add_buff(actual_debuff)



func get_attribute_set() -> AttributeSet:
	if is_instance_valid(actual_debuff):
		return actual_debuff.get_attribute_set()
	return null
