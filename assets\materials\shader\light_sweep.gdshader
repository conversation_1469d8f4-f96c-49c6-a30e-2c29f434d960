shader_type canvas_item;
render_mode blend_mix;

// 基础控制参数
uniform float sweep_speed : hint_range(0.0, 20.0) = 9.0; // 扫光移动速度
uniform float intensity : hint_range(0.0, 2.0) = 0.9; // 扫光整体强度
uniform float brightness : hint_range(0.0, 1.0) = 0.6; // 扫光亮度倍数
uniform vec4 sweep_color : source_color = vec4(1.0, 1.0, 1.0, 1.0); // 扫光颜色
uniform vec2 direction = vec2(1.0, 1.0); // 扫光方向 (x, y)

// 精细控制参数
uniform float sweep_interval : hint_range(0.1, 20.0) = 3.0; // 扫光间隔：控制扫光条纹之间的距离，值越大间隔越大
uniform float sweep_width : hint_range(0.1, 5.0) = 1.0; // 扫光粗细：控制每条扫光的宽度，值越大扫光越粗
uniform float edge_sharpness : hint_range(0.1, 10.0) = 2.0; // 边缘锐利度：控制扫光边缘的锐利程度，值越大边缘越锐利


void vertex() {
	// Called for every vertex the material is visible on.
}

void fragment() {

	// 获取标准化的UV坐标
	vec2 uv = UV;

	// 计算基础位置（方向控制）
	float base_position = uv.y * direction.y + uv.x * direction.x;

	// 添加时间偏移实现扫光移动
	base_position -= TIME * sweep_speed;

	// 应用间距控制：使用频率来控制条纹间距，不影响波形形状
	// sweep_interval 值越大，频率越低，间距越大
	float frequency = 1.0 / sweep_interval;
	float sweep_wave = sin(base_position * frequency);

	// 应用扫光宽度控制：独立控制每条扫光的宽度
	// sweep_width越大，扫光越粗；通过pow函数实现
	float width_factor = 1.0 / sweep_width;
	sweep_wave = pow(abs(sweep_wave), width_factor) * sign(sweep_wave);

	// 应用边缘锐利度控制：独立控制扫光边缘的锐利程度
	sweep_wave = pow(abs(sweep_wave), 1.0 / edge_sharpness) * sign(sweep_wave);

	// 应用强度和亮度
	float sweep = sweep_wave * intensity;
	sweep *= sweep * sweep * brightness;

	// 限制扫光值在0到1之间
	sweep = clamp(sweep, 0.0, 1.0);

	// 输出最终颜色
	COLOR = vec4(sweep * sweep_color.rgb, sweep * sweep_color.a);
}
