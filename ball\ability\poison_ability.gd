class_name PoisonAbility
extends Ball<PERSON>bilityBase

@export var debuff_template: AttributeBuffDOT
var actual_debuff: AttributeBuffDOT

func _ready() -> void:
	actual_debuff = debuff_template.duplicate_buff()


var level: int:
	get:
		return actual_debuff.get_attribute_value("level") as int
	set(new_level):
		actual_debuff.set_attribute_value("level", new_level)

func on_primary_hit(source: BallBase, target: EnemyBase) -> void:
	if debuff_template == null:
		push_warning("debuff_template is not set in PoisonAbility")
		return

	if target.has_method("add_buff"):
		target.add_buff(actual_debuff)


func on_secondary_hit(source: BallBase, target: EnemyBase, source_ability: BallAbilityBase) -> void:
	if debuff_template == null:
		push_warning("debuff_template is not set in PoisonAbility")
		return

	if target.has_method("add_buff"):
		target.add_buff(actual_debuff)


func get_attribute_set() -> AttributeSet:
	if is_instance_valid(actual_debuff):
		return actual_debuff.attribute_set
	return null
